<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\Plan\PlanMetaData;
use App\Entity\Trait\HasCollectionTrait;
use App\Repository\PlanRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Plan.
 *
 * Represents a plan in the system. Maps to the `plans` table in the database.
 */
#[ORM\Entity(repositoryClass: PlanRepository::class)]
#[ORM\Table(name: 'plans')]
class Plan extends BaseEntity
{

    use HasCollectionTrait;

    #[ORM\Embedded(class: PlanMetaData::class, columnPrefix: false)]
    private PlanMetaData $metaData;

    public function __construct(string $shortcut)
    {
        $this->metaData = new PlanMetaData($shortcut);
    }

    public function loadMetaData(): PlanMetaData
    {
        return $this->metaData;
    }

}
