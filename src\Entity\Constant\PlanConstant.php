<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class PlanConstant.
 *
 * This class holds all constant values related to the Plan entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing plan-specific data.
 */
final class PlanConstant
{

    final public const int TYPE_TRIAL = 0;

    final public const int TYPE_MONTHLY = 1;

    final public const int TYPE_QUARTERLY = 2;

    final public const int TYPE_BIANNUALLY = 3;

    final public const int TYPE_ANNUALLY = 4;

    final public const array TYPES
        = [
            self::TYPE_TRIAL      => 'Trial',
            self::TYPE_MONTHLY    => 'Monthly',
            self::TYPE_QUARTERLY  => 'Quarterly',
            self::TYPE_BIANNUALLY => 'Biannually',
            self::TYPE_ANNUALLY   => 'Annually',
        ];

}
