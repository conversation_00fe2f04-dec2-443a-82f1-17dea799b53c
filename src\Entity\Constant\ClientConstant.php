<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class ClientConstant.
 *
 * This class holds all constant values related to the Client entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing client-specific data.
 */
final class ClientConstant
{

    final public const int AUTO_RENEW_DISABLE = 0;

    final public const int AUTO_RENEW_ENABLE = 1;

    final public const array TYPES
        = [
            self::AUTO_RENEW_DISABLE => 'Disabled',
            self::AUTO_RENEW_ENABLE  => 'Enabled',
        ];

}
