<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Plan;

use App\Entity\Trait\HasTypeTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class PlanMetaData.
 *
 * This class represents meta-data about a plan.
 *
 * It is used as an embeddable entity in the Plan entity.
 */
#[ORM\Embeddable]
final class PlanMetaData
{

    use HasTypeTrait;

    #[ORM\Column(name: 'shortcut', length: 16, unique: true)]
    private readonly string $shortcut;

    #[ORM\Column(name: 'name', length: 255)]
    private string $name;

    #[ORM\Column(name: 'description', type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(name: 'price', type: 'decimal', precision: 65, scale: 2, options: ['default' => 0.00])]
    private string $price = '0.00';

    #[ORM\Column(name: 'length', length: 20)]
    private string $length;

    public function __construct(string $shortcut)
    {
        $this->shortcut = $shortcut;
    }

    public function getShortcut(): string
    {
        return $this->shortcut;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): PlanMetaData
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): PlanMetaData
    {
        $this->description = $description;

        return $this;
    }

    public function getPrice(): float
    {
        return (float)($this->price ?? '0.00');
    }

    public function setPrice(float $price = 0.00): PlanMetadata
    {
        $this->price = number_format($price, 2, '.', '');

        return $this;
    }

    public function getLength(): string
    {
        return $this->length;
    }

    public function setLength(string $length): PlanMetaData
    {
        $this->length = $length;

        return $this;
    }

}
