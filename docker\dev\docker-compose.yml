services:
    nginx:
        image: nginx:latest
        container_name: zenshop_nginx
        ports:
            - ${DOCKER_NGINX_PORT}:80
        volumes:
            - ./:/var/www/zenshop:cached
            - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
        depends_on:
            - app
        networks:
            - zenshop_network
        extra_hosts:
            - ${DOCKER_WEB_HOST}:127.0.0.1
        hostname: ${DOCKER_WEB_HOST}

    app:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: zenshop_php
        volumes:
            - ./:/var/www/zenshop:cached
            - /var/www/zenshop/vendor
            - /var/www/v/var/cache
            - /var/www/zenshop/var/log
            - ~/.composer-docker/cache:/root/.composer/cache:delegated
        depends_on:
            - mariadb
            - redis
        networks:
            - zenshop_network

    mariadb:
        image: mariadb:11
        container_name: zenshop_mariadb
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
            MYSQL_DATABASE: ${DB_NAME}
            MYSQL_USER: ${DB_USER}
            MYSQL_PASSWORD: ${DB_PASSWORD}
        volumes:
            - mariadb_data:/var/lib/mysql
            - ./docker/mariadb/init:/docker-entrypoint-initdb.d:ro
            - ./docker/mariadb/my.cnf:/etc/mysql/conf.d/my.cnf:ro
        ports:
            - ${DOCKER_SQL_PORT}:3306
        networks:
            - zenshop_network
        command: --default-authentication-plugin=mysql_native_password

    redis:
        image: redis:latest
        container_name: zenshop_redis
        restart: always
        volumes:
            - redis_data:/var/lib/redis
            - ./docker/redis/default.conf:/usr/local/etc/redis/redis.conf
        command: redis-server /usr/local/etc/redis/redis.conf
        ports:
            - ${DOCKER_REDIS_PORT}:6379
        networks:
            - zenshop_network

networks:
    zenshop_network:
        driver: bridge
        ipam:
            config:
                -   subnet: **********/16

volumes:
    mariadb_data:
        driver: local
    redis_data:
        driver: local
