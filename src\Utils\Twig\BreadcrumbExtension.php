<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/20/2025
 * @time 7:29 PM
 */

namespace App\Utils\Twig;

use Twig\Environment;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class BreadcrumbExtension extends AbstractExtension
{

    private Environment $twig;

    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('breadcrumb', $this->renderBreadcrumb(...), ['is_safe' => ['html']]),
        ];
    }

    /**
     * @throws \Twig\Error\SyntaxError
     * @throws \Twig\Error\RuntimeError
     * @throws \Twig\Error\LoaderError
     */
    public function renderBreadcrumb(array $items): string
    {
        return $this->twig->render('domains/admin/shared/widgets/breadcrumb.html.twig', [
            'items' => $items,
        ]);
    }

}
