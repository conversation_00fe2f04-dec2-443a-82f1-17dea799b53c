<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    05/06/2025
 * @time    15:40
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Data Transfer Object (DTO) for user wallet information.
 *
 * This DTO holds essential details about a user's digital wallet, primarily
 * focusing on the current credit balance and relevant timestamps. It's used
 * to convey wallet status in administrative panels or user dashboards.
 */
final class UserWalletDTO
{
    public string|float|null $credit;
    public ?string $createdAt;
    public ?string $updatedAt;
}
