<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\UserProfile;

use App\Entity\Constant\UserProfileConstant;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserProfileMetaData.
 *
 * This class represents meta-data about a user-profile.
 *
 * It is used as an embeddable entity in the UserProfile entity.
 */
#[ORM\Embeddable]
final class UserProfileMetaData
{

    #[ORM\Column(name: 'avatar', type: 'text', length: 255, nullable: true)]
    private ?string $avatar = null;

    #[ORM\Column(name: 'first_name', type: 'string', length: 100)]
    private string $firstName;

    #[ORM\Column(name: 'last_name', type: 'string', length: 100)]
    private string $lastName;

    #[ORM\Column(name: 'dob', type: 'string', length: 10, nullable: true)]
    private ?string $dob = null;

    #[ORM\Column(name: 'biography', type: 'string', length: 500, nullable: true)]
    private ?string $biography = null;

    #[ORM\Column(name: 'gender', type: 'integer', options: ['default' => UserProfileConstant::GENDER_MALE])]
    private int $gender = UserProfileConstant::GENDER_MALE;

    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    public function setAvatar(?string $avatar): UserProfileMetaData
    {
        $this->avatar = $avatar;

        return $this;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): UserProfileMetaData
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): UserProfileMetaData
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getDob(): ?string
    {
        return $this->dob;
    }

    public function setDob(?string $dob): UserProfileMetaData
    {
        $this->dob = $dob;

        return $this;
    }

    public function getBiography(): ?string
    {
        return $this->biography;
    }

    public function setBiography(?string $biography): UserProfileMetaData
    {
        $this->biography = $biography;

        return $this;
    }

    public function getGender(): int
    {
        return $this->gender;
    }

    public function setGender(int $gender = UserProfileConstant::GENDER_MALE): UserProfileMetaData
    {
        $this->gender = $gender;

        return $this;
    }

}
