<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Currency;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class CurrencyMetaData.
 *
 * This class represents meta-data about a currency.
 *
 * It is used as an embeddable entity in the Currency entity.
 */
#[ORM\Embeddable]
final class CurrencyMetaData
{

    #[ORM\Column(name: 'code', length: 3, unique: true)]
    private readonly string $code;

    #[ORM\Column(name: 'name', length: 255)]
    private string $name;

    #[ORM\Column(name: 'symbol', length: 10)]
    private string $symbol;

    #[ORM\Column(name: 'decimal_places', type: 'integer', options: ['default' => 2])]
    private int $decimalPlaces = 2;

    #[ORM\Column(name: 'exchange_rate_to_usd', type: 'decimal', precision: 12, scale: 6, options: ['default' => 1.000000])]
    private string $exchangeRateToUsd = '1.000000';

    public function __construct(
        string $code
    ) {
        $this->code = $code;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): CurrencyMetaData
    {
        $this->name = $name;

        return $this;
    }

    public function getSymbol(): string
    {
        return $this->symbol;
    }

    public function setSymbol(string $symbol): CurrencyMetaData
    {
        $this->symbol = $symbol;

        return $this;
    }

    public function getDecimalPlaces(): int
    {
        return $this->decimalPlaces;
    }

    public function setDecimalPlaces(int $decimalPlaces = 2): CurrencyMetaData
    {
        $this->decimalPlaces = $decimalPlaces;

        return $this;
    }

    public function getExchangeRateToUsd(): float
    {
        return (float)($this->exchangeRateToUsd ?? '1.000000');
    }

    public function setPrice(float $exchangeRateToUsd = 1.000000): CurrencyMetaData
    {
        $this->exchangeRateToUsd = number_format($exchangeRateToUsd, 2, '.', '');

        return $this;
    }

}
