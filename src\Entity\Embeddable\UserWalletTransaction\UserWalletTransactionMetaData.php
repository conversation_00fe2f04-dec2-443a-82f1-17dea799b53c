<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\UserWalletTransaction;

use App\Entity\Trait\HasTypeTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserWalletTransactionMetaData.
 *
 * This class represents meta-data about a user-wallet-transaction.
 *
 * It is used as an embeddable entity in the UserWalletTransaction entity.
 */
#[ORM\Embeddable]
final class UserWalletTransactionMetaData
{

    use HasTypeTrait;

    #[ORM\Column(name: 'amount', type: 'decimal', precision: 65, scale: 2)]
    private string $amount;

    #[ORM\Column(name: 'credit', type: 'decimal', precision: 65, scale: 2)]
    private string $credit;

    #[ORM\Column(name: 'json_data', type: 'json')]
    private array $jsonData;

    public function getAmount(): float
    {
        return (float)$this->amount;
    }

    public function setAmount(float $amount): UserWalletTransactionMetaData
    {
        $this->amount = number_format($amount, 2, '.', '');

        return $this;
    }

    public function getCredit(): float
    {
        return (float)$this->credit;
    }

    public function setCredit(float $credit): UserWalletTransactionMetaData
    {
        $this->credit = number_format($credit, 2, '.', '');

        return $this;
    }

    public function getJsonData(): array
    {
        return $this->jsonData;
    }

    public function setJsonData(array $jsonData): UserWalletTransactionMetaData
    {
        $this->jsonData = $jsonData;

        return $this;
    }

}
