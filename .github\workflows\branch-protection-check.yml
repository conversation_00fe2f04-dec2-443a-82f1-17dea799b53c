name: Branch Protection Check

# Add permissions configuration for GITHUB_TOKEN
permissions:
    contents: read
    issues: write
    pull-requests: write

on:
    push:
        branches:
            - staging
            - main
    pull_request:
        branches:
            - staging
            - main

jobs:
    check-exempted-users:
        runs-on: ubuntu-latest
        outputs:
            is_exempted: ${{ steps.check-user.outputs.IS_EXEMPTED }}
        steps:
            -   name: Check if user is exempted
                id: check-user
                run: |
                    if [[ "${{ github.actor }}" == "evo-master" || "${{ github.actor }}" == "m397dev" ]]; then
                      echo "IS_EXEMPTED=true" >> $GITHUB_OUTPUT
                      echo "::notice::User ${{ github.actor }} is exempted from branch protection checks."
                    else
                      echo "IS_EXEMPTED=false" >> $GITHUB_OUTPUT
                    fi

    check-branch-protection:
        runs-on: ubuntu-latest
        needs: check-exempted-users
        if: needs.check-exempted-users.outputs.is_exempted != 'true' && github.event_name == 'push'
        steps:
            -   name: Check for direct push to protected branch
                id: check-push
                run: |
                    if [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
                      echo "VIOLATION=true" >> $GITHUB_OUTPUT
                      echo "BRANCH=staging" >> $GITHUB_OUTPUT
                      echo "::warning::Direct push to staging branch detected! Please use Pull Request instead of pushing directly."
                      exit 1
                    elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
                      echo "VIOLATION=true" >> $GITHUB_OUTPUT
                      echo "BRANCH=main" >> $GITHUB_OUTPUT
                      echo "::warning::Direct push to main branch detected! Please use Pull Request from staging instead of pushing directly."
                      exit 1
                    fi

            # Send Slack notification (if violation detected and slack webhook is configured)
            -   name: Send Slack notification
                if: failure() && steps.check-push.outputs.VIOLATION == 'true' && env.HAS_SLACK_WEBHOOK == 'true'
                uses: 8398a7/action-slack@v3
                with:
                    status: custom
                    fields: repo,message,commit,author
                    custom_payload: |
                        {
                          "attachments": [
                            {
                              "color": "#FF0000",
                              "title": "❌ Branch Protection Violation",
                              "text": "Direct push to ${{ steps.check-push.outputs.BRANCH }} branch detected by @${{ github.actor }}.\nRepository: ${{ github.repository }}\nCommit: ${{ github.sha }}",
                              "footer": "GitHub Actions",
                              "footer_icon": "https://github.githubassets.com/favicon.ico"
                            }
                          ]
                        }
                env:
                    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
                    HAS_SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL != '' }}
                continue-on-error: true

            # Create warning issue
            -   name: Create Issue
                if: failure() && steps.check-push.outputs.VIOLATION == 'true'
                uses: actions/github-script@v6
                with:
                    github-token: ${{ secrets.GITHUB_TOKEN }}
                    script: |
                        const branch = "${{ steps.check-push.outputs.BRANCH }}";
                        await github.rest.issues.create({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          title: `🚨 Branch Protection Violation: Direct push to ${branch} branch`,
                          body: `
                          # Branch Protection Violation

                          **Actor:** @${{ github.actor }}
                          **Affected Branch:** ${branch}
                          **Commit ID:** ${{ github.sha }}
                          **Timestamp:** ${{ github.event.head_commit.timestamp }}

                          ### Reminder
                          - staging branch: Create PR from feature branch
                          - main branch: Create PR from staging branch

                          Please follow the branch protection rules in this repository.
                          `
                        });

            # Check if all email-related secrets are available
            -   name: Check email configuration
                id: check-email
                run: |
                    if [[ -n "${{ secrets.MAIL_SERVER }}" && -n "${{ secrets.MAIL_PORT }}" && -n "${{ secrets.MAIL_USERNAME }}" && -n "${{ secrets.MAIL_PASSWORD }}" && -n "${{ secrets.NOTIFICATION_EMAIL }}" ]]; then
                      echo "HAS_EMAIL_CONFIG=true" >> $GITHUB_OUTPUT
                    else
                      echo "HAS_EMAIL_CONFIG=false" >> $GITHUB_OUTPUT
                    fi

            # Send email notification (only if all email secrets are available)
            -   name: Send email notification
                if: failure() && steps.check-push.outputs.VIOLATION == 'true' && steps.check-email.outputs.HAS_EMAIL_CONFIG == 'true'
                uses: dawidd6/action-send-mail@v3
                with:
                    server_address: ${{ secrets.MAIL_SERVER }}
                    server_port: ${{ secrets.MAIL_PORT }}
                    username: ${{ secrets.MAIL_USERNAME }}
                    password: ${{ secrets.MAIL_PASSWORD }}
                    subject: "🚨 [GitHub] Branch Protection Violation in ${{ github.repository }}"
                    body: |
                        Direct push to ${{ steps.check-push.outputs.BRANCH }} branch detected by ${{ github.actor }}.

                        Repository: ${{ github.repository }}
                        Commit: ${{ github.sha }}
                        Timestamp: ${{ github.event.head_commit.timestamp }}

                        Reminder:
                        - staging branch: Create PR from feature branch
                        - main branch: Create PR from staging branch
                    to: ${{ secrets.NOTIFICATION_EMAIL }}
                    from: GitHub Actions
                continue-on-error: true

    check-pull-request-source:
        runs-on: ubuntu-latest
        needs: check-exempted-users
        if: needs.check-exempted-users.outputs.is_exempted != 'true' && github.event_name == 'pull_request'
        steps:
            -   name: Check PR source for main branch
                if: github.event.pull_request.base.ref == 'main'
                id: check-pr-main
                run: |
                    if [[ "${{ github.event.pull_request.head.ref }}" != "staging" ]]; then
                      echo "VIOLATION=true" >> $GITHUB_OUTPUT
                      echo "::warning::Pull Requests to main must come from staging branch. Current source: ${{ github.event.pull_request.head.ref }}"
                      exit 1
                    fi

            # Add warning comment to PR
            -   name: Comment on PR
                if: failure() && steps.check-pr-main.outputs.VIOLATION == 'true'
                uses: actions/github-script@v6
                with:
                    github-token: ${{ secrets.GITHUB_TOKEN }}
                    script: |
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          body: `
                          ## ⚠️ Pull Request Rule Violation

                          Pull Requests to main branch must come from staging branch.

                          - Current target branch: main
                          - Current source branch: ${{ github.event.pull_request.head.ref }}

                          Please create a new Pull Request from staging to main.
                          `
                        });
