<?php

declare(strict_types=1);

namespace App\Library\ReCaptcha;

use App\Library\ReCaptcha\Contract\ReCaptchaServiceInterface;
use ReCaptcha\ReCaptcha;

/**
 * Class ReCaptchaService
 *
 * This class provides a service for handling Google reCAPTCHA v3.
 */
class ReCaptchaService implements ReCaptchaServiceInterface
{

    public bool $isEnable = false;

    private ?ReCaptcha $recaptcha = null;

    public function getSiteKey(): ?string
    {
        return $_ENV['GOOGLE_RECAPTCHA_SITE_KEY'] ?? null;
    }

    public function getSecretKey(): ?string
    {
        return $_ENV['GOOGLE_RECAPTCHA_SECRET'] ?? null;
    }

    public function getThreshold(): float
    {
        return (float)($_ENV['GOOGLE_RECAPTCHA_THRESHOLD'] ?? 0.5);
    }

    public function getHostname(): ?string
    {
        $baseURL = $_ENV['APP_BASE_URL'] ?? null;

        if (filter_var($baseURL, FILTER_VALIDATE_URL) === false) {
            return null;
        }

        $parsedUrl = parse_url($baseURL);
        return $parsedUrl['host'] ?? null;
    }

    public function getScript(): ?string
    {
        if (!$this->isEnable) {
            return null;
        }

        $siteKey = $this->getSiteKey();
        $recaptchaInput = 'input[name$="[recaptcha]"]';

        return "
        <script src='https://www.google.com/recaptcha/api.js?render=".$siteKey."'></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.querySelectorAll('form').forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        event.preventDefault()

                        grecaptcha.execute('".$siteKey."', { action: 'submit_form' }).then(function (token) {
                            let recaptchaInput = form.querySelector('".$recaptchaInput."')

                            if (recaptchaInput) {
                                recaptchaInput.value = token
                            }

                            form.submit()
                        })
                    })
                })
            })
        </script>
        ";
    }

    public function validate(string $response, array $data = []): bool
    {
        if (!$this->isEnable) {
            return false;
        }

        $action = $data['action'] ?? 'submit_form';
        $recaptcha = $this->prepare($action);

        if ($recaptcha === null) {
            return false;
        }

        $remoteIp = $this->resolveRemoteIp($data);

        return $recaptcha->verify($response, $remoteIp)->isSuccess();
    }

    /**
     * Prepares the ReCaptcha instance with the expected hostname and action.
     *
     * @param  string  $action  The action to be set
     *
     * @return ReCaptcha|null
     */
    private function prepare(string $action): ?ReCaptcha
    {
        if ($this->recaptcha === null) {
            $this->recaptcha = $this->createInstance();
        }

        return $this->recaptcha
            ?->setExpectedHostname($this->getHostname())
            ->setExpectedAction($action)
            ->setScoreThreshold($this->getThreshold());
    }

    /**
     * Creates a new ReCaptcha instance if the secret key is set.
     *
     * @return ReCaptcha|null
     */
    private function createInstance(): ?ReCaptcha
    {
        $secretKey = $this->getSecretKey();

        return $secretKey !== null ? new ReCaptcha($secretKey) : null;
    }

    /**
     * Resolves the remote IP address from the provided data.
     *
     * @param  array  $data  The data containing the remote IP address
     *
     * @return string|null
     */
    private function resolveRemoteIp(array $data): ?string
    {
        if (!isset($data['remoteIp'])) {
            return null;
        }

        return filter_var($data['remoteIp'], FILTER_VALIDATE_IP) ? $data['remoteIp'] : null;
    }

}

