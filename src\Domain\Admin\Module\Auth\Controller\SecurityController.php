<?php

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Exception\LogicalException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

/**
 * Class SecurityController.
 *
 * Handles the security-related actions for the admin module.
 * This includes user login and logout functionalities.
 */
#[Route('/admin', name: 'app_admin_auth_')]
class SecurityController extends AdminController
{

    /**
     * Displays the login page and handles user authentication.
     *
     * @param Request $request The current request object.
     * @param AuthenticationUtils $authenticationUtils The authentication utility service.
     *
     * @return Response The rendered login page or a redirect if already authenticated.
     */
    #[Route('/login', name: 'login', methods: ['GET', 'POST'])]
    public function index(Request $request, AuthenticationUtils $authenticationUtils): Response
    {
        if ($this->getUser()) {
            return $this->redirectToRoute('app_admin_dashboard_index');
        }

        $error = $authenticationUtils->getLastAuthenticationError();

        if ($error && $request->isMethod('POST')) {
            flash()->error("Incorrect email or password. Please try again.");

            return $this->redirectToRoute('app_admin_auth_login');
        }

        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->view('/auth/pages/login', [
            'last_username' => $lastUsername,
        ]);
    }

    /**
     * Handles user logout.
     *
     * This method is intentionally left blank. The actual logout logic is handled by Symfony's security system.
     * A success message is displayed upon logout.
     *
     * @return Response A response indicating the user has been logged out.
     *
     * @throws LogicalException If this method is called directly, which should not happen.
     */
    #[Route('/logout', name: 'logout', methods: ['GET', 'POST'])]
    public function logout(): Response
    {
        flash()->success('Logout successfully!');

        throw new LogicalException(
            'This method should be blank - it will be intercepted by the logout key on your firewall.'
        );
    }

}
