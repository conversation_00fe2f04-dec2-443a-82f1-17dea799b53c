<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email  <EMAIL>
 * @date    6/18/2025
 * @time    7:12 PM
 */

namespace App\Domain\Admin\Module\AccountManagement\Contract;

use App\Domain\Admin\Module\AccountManagement\DTO\ListAccountDTO;

/**
 * Interface ListAccountServiceInterface.
 *
 * This interface defines the contract for the account management service.
 * It provides methods for retrieving account lists, changing account statuses,
 * and performing bulk actions on user accounts.
 *
 */
interface ListAccountServiceInterface
{

    /**
     * Retrieve a list of all user accounts.
     *
     * The list excludes accounts with the admin role.
     *
     * @return ListAccountDTO|null Returns a Data Transfer Object containing a list of user accounts, or null if no
     *                             accounts are found
     */
    public function getList(): ?ListAccountDTO;

}
