<?php

declare(strict_types=1);

namespace App\Base;

use App\Library\ReCaptcha\Contract\ReCaptchaServiceInterface;
use App\Library\ReCaptcha\ReCaptchaService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;

/**
 * Abstract class BaseController.
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 */
abstract class BaseController extends AbstractController
{

    /**
     * The domain name for the module.
     *
     * This is used to determine the path for rendering views.
     *
     * @var string
     */
    protected string $domainName = 'portal';

    /**
     * The ReCaptcha service instance.
     *
     * This service is used to handle Google reCAPTCHA functionality.
     *
     * @var ReCaptchaServiceInterface
     */
    private readonly ReCaptchaServiceInterface $reCaptchaService;

    public function __construct()
    {
        $this->reCaptchaService = new ReCaptchaService();
    }

    /**
     * Render a view with the given parameters.
     *
     * @param  string         $view        The view template to render
     * @param  array          $parameters  The parameters to pass to the view
     * @param  Response|null  $response    An optional response object
     *
     * @return Response The rendered response
     */
    public function view(string $view, array $parameters = [], ?Response $response = null): Response
    {
        $moduleView = 'domains/'.strtolower($this->domainName).'/modules/'.$view.'.html.twig';

        if ($this->reCaptchaService->isEnable) {
            $parameters['captchaWidget'] = $this->reCaptchaService->getScript();
        }

        return $this->render($moduleView, $parameters, $response);
    }

}
