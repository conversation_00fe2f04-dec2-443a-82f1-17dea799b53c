<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\CreditPackage;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class CreditPackageMetaData.
 *
 * This class represents meta-data about a credit-package.
 *
 * It is used as an embeddable entity in the CreditPackage entity.
 */
#[ORM\Embeddable]
class CreditPackageMetaData
{

    #[ORM\Column(name: 'shortcut', length: 16, unique: true)]
    private readonly string $shortcut;

    #[ORM\Column(name: 'name', length: 255)]
    private string $name;

    #[ORM\Column(name: 'description', type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(name: 'price', type: 'decimal', precision: 65, scale: 2, options: ['default' => 0.00])]
    private string $price = '0.00';

    #[ORM\Column(name: 'credit', type: 'decimal', precision: 65, scale: 2, options: ['default' => 0.00])]
    private string $credit = '0.00';

    #[ORM\Column(name: 'store_limit', type: 'integer', options: ['default' => 10])]
    private int $storeLimit = 10;

    #[ORM\Column(name: 'order_limit', type: 'integer', options: ['default' => 1000])]
    private int $orderLimit = 1000;

    public function __construct(string $shortcut)
    {
        $this->shortcut = $shortcut;
    }

    public function getShortcut(): string
    {
        return $this->shortcut;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): CreditPackageMetaData
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): CreditPackageMetaData
    {
        $this->description = $description;

        return $this;
    }

    public function getPrice(): float
    {
        return (float)($this->price ?? '0.00');
    }

    public function setPrice(float $price = 0.00): CreditPackageMetaData
    {
        $this->price = number_format($price, 2, '.', '');

        return $this;
    }

    public function getCredit(): float
    {
        return (float)($this->credit ?? '0.00');
    }

    public function setCredit(float $credit): CreditPackageMetaData
    {
        $this->credit = number_format($credit, 2, '.', '');

        return $this;
    }

    public function getStoreLimit(): int
    {
        return $this->storeLimit;
    }

    public function setStoreLimit(int $storeLimit): CreditPackageMetaData
    {
        $this->storeLimit = $storeLimit;

        return $this;
    }

    public function getOrderLimit(): int
    {
        return $this->orderLimit;
    }

    public function setOrderLimit(int $orderLimit): CreditPackageMetaData
    {
        $this->orderLimit = $orderLimit;

        return $this;
    }

}
