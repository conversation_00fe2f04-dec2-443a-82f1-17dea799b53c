<ul class="hidden flex-wrap items-center space-x-2 sm:flex">
    <li class="flex items-center space-x-2">
        <a
            class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
            href="javascript:void(0);"
            @click.prevent="navigateToPage('{{ app_admin_base_url }}', 'dashboard')"
        >Dashboard</a
        >
        <svg
            x-ignore
            xmlns="http://www.w3.org/2000/svg"
            class="size-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
        >
            <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
            ></path>
        </svg>
    </li>
    {% for item in items %}
        {% if loop.last %}
            <li>{{ item.name }}</li>
        {% else %}
            <li class="flex items-center space-x-2">
                <a
                    class="text-primary transition-colors hover:text-primary-focus dark:text-accent-light dark:hover:text-accent"
                    href="javascript:void(0);"
                    @click.prevent="navigateToPage('{{ item.url }}', '{{ item.sidebar }}')"
                >{{ item.name }}</a
                >
                <svg
                    x-ignore
                    xmlns="http://www.w3.org/2000/svg"
                    class="size-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                    ></path>
                </svg>
            </li>
        {% endif %}
    {% endfor %}
</ul>
