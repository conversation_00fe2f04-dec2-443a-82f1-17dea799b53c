<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\UserWallet\UserWalletMetaData;
use App\Entity\Trait\HasLifeCycleTrait;
use App\Repository\UserWalletRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserWallet.
 *
 * Represents a client in the system. Maps to the `user_wallets` table in the database.
 */
#[ORM\Entity(repositoryClass: UserWalletRepository::class)]
#[ORM\Table(name: 'user_wallets')]
#[ORM\HasLifecycleCallbacks]
class UserWallet extends BaseEntity
{

    use HasLifeCycleTrait;

    #[ORM\OneToMany(targetEntity: UserWalletTransaction::class, mappedBy: 'userWallet', cascade: ['persist', 'remove'])]
    private Collection $userWalletTransactions;

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: 'userWallet')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'RESTRICT')]
    private User $user;

    #[ORM\Embedded(class: UserWalletMetaData::class, columnPrefix: false)]
    private UserWalletMetaData $metaData;

    public function __construct()
    {
        $this->metaData = new UserWalletMetaData();

        $this->userWalletTransactions = new ArrayCollection();
    }

    public function getUserWalletTransactions(): Collection
    {
        return $this->userWalletTransactions;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function loadMetaData(): UserWalletMetaData
    {
        return $this->metaData;
    }

}
