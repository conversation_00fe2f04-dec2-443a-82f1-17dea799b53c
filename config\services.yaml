parameters:
    app.base_url: '%env(APP_BASE_URL)%'
    google_recaptcha_site_key: '%env(GOOGLE_RECAPTCHA_SITE_KEY)%'

services:
    _defaults:
        autowire: true
        autoconfigure: true

    App\:
        resource: '../src/'

    App\Domain\Admin\:
        resource: '../src/Domain/Admin/'

    App\Domain\Client\:
        resource: '../src/Domain/Client/'

    App\Domain\Portal\:
        resource: '../src/Domain/Portal/'

    Database\Factory\:
        resource: '../database/factories/'

    App\Domain\Admin\Module\Auth\Security\Handler\AdminAuthenticationFailureHandler:
        arguments:
            $adminLimiter: '@limiter.admin_limiter'

    App\Domain\Admin\Module\Auth\Security\Handler\AdminAuthenticationSuccessHandler:
        arguments:
            $adminLimiter: '@limiter.admin_limiter'

    App\Domain\Admin\Module\Auth\Security\Checker\AdminUserChecker:
        arguments:
            $adminLimiter: '@limiter.admin_limiter'

    App\Domain\Admin\Module\Auth\Controller\ChangePasswordController:
        arguments:
            $passwordChangeLimiter: '@limiter.password_change_limiter'

    Redis:
        class: Redis
        calls:
            - [ connect, [ '%env(REDIS_HOST)%', '%env(int:REDIS_PORT)%' ] ]

    App\Domain\Admin\Module\Auth\Service\RedisSessionManagerService:
        arguments:
            $redis: '@Redis'
            $logger: '@logger'
