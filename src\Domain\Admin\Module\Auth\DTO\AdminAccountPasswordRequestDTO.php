<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    01:04
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\DTO;

use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class AdminAccountPasswordRequestDTO.
 *
 * This class represents a Data Transfer Object (DTO) for handling password change requests
 * in the admin account management context. It contains properties for the current password,
 * new password, and confirmation of the new password.
 */
final class AdminAccountPasswordRequestDTO
{

    #[Assert\NotBlank(message: 'Current password is required.')]
    #[Assert\Length(
        min: 1,
        max: 255,
        minMessage: 'Current password must be at least {{ limit }} characters long.',
        maxMessage: 'Current password cannot be longer than {{ limit }} characters.'
    )]
    public ?string $currentPassword = null;

    #[Assert\NotBlank(message: 'New password is required.')]
    #[Assert\Length(
        min: 8,
        max: 255,
        minMessage: 'New password must be at least {{ limit }} characters long.',
        maxMessage: 'New password cannot be longer than {{ limit }} characters.'
    )]
    #[Assert\NotEqualTo(
        propertyPath: 'currentPassword',
        message: 'New password must be different from the current password.'
    )]
    #[Assert\Regex(
        pattern: '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
        message: 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.'
    )]
    public ?string $newPassword = null;

    #[Assert\NotBlank(message: 'Confirm password is required.')]
    #[Assert\Length(
        min: 8,
        max: 255,
        minMessage: 'Confirm password must be at least {{ limit }} characters long.',
        maxMessage: 'Confirm password cannot be longer than {{ limit }} characters.'
    )]
    #[Assert\EqualTo(
        propertyPath: 'newPassword',
        message: 'The password fields must match.'
    )]
    public ?string $confirmPassword = null;

}
