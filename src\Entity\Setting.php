<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\Setting\SettingMetaData;
use App\Repository\SettingRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Client.
 *
 * Represents a client in the system. Maps to the `clients` table in the database.
 */
#[ORM\Entity(repositoryClass: SettingRepository::class)]
#[ORM\Table(name: 'settings')]
class Setting extends BaseEntity
{

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'settings')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: true, onDelete: 'CASCADE')]
    private ?User $user = null;

    #[ORM\Embedded(class: SettingMetaData::class, columnPrefix: false)]
    private SettingMetaData $metaData;

    public function __construct(string $dataKey, array $dataValue)
    {
        $this->metaData = new SettingMetaData($dataKey, $dataValue);
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function loadMetaData(): SettingMetaData
    {
        return $this->metaData;
    }

}
