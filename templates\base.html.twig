<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta content="{{ app_name }}" name="keywords">
    <meta content="{{ app_name }}" name="description">
    <meta content="{{ app_name }}" name="author">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>

    <title>{% block title %}Welcome!{% endblock %} | {{ app_name }} - High-performance and fully flexible automated
        e-commerce platform</title>

    {% block importmap %}{{ importmap('app') }}{% endblock %}

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <script>
        localStorage.getItem('_x_darkMode_on') === 'true' &&
        document.documentElement.classList.add('dark')
    </script>

    {% block stylesheets %}{% endblock %}
</head>
<body class="is-header-blur" x-data x-bind="$store.global.documentBody">

<div id="root" class="min-h-100vh flex grow bg-slate-50 dark:bg-navy-900" x-cloak>
    {% block body %}{% endblock %}
</div>

<div id="x-teleport-target"></div>

<script>
    window.addEventListener('DOMContentLoaded', () => Alpine.start())
</script>

{% block javascripts %}{% endblock %}

{% if captchaWidget is defined %}
    {{ captchaWidget | raw }}
{% endif %}
</body>
</html>
