:root {
    --pink03: rgba(255, 220, 220, 0.3);
    --dusty-rose: #DA6C6C;
    --muted-rose: #AF3E3E;
    --muted-primary: #0D5EA6;
    --blush-pink: #FCD8CD;
    --blush-pink-rgb: 252, 216, 205;
    --default-border: #ecf3fb;
}

.card.custom-card {
    border-radius: 0.5rem;
    box-shadow: 0 6px 16px 2px rgba(0, 0, 0, 0.05);
    border: 0;
    margin-block-end: 1.5rem;
    overflow: hidden;
}

.profile-banner-card {
    position: relative;
}

.profile-banner-card::before {
    content: "";
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
    width: 100%;
    height: 100%;
    background-color: var(--pink03);
}

.profile-content {
    margin-block-start: -5rem;
}

.bg-primary-70 {
    background-color: color-mix(in srgb, var(--color-primary), transparent 70%);
}

.dark .bg-primary-70 {
    background-color: color-mix(in srgb, var(--color-primary), transparent 30%);
}

.bg-primary-icon {
    background-color: color-mix(in srgb, var(--color-primary), transparent 70%);
}

.dark .bg-primary-icon {
    background-color: color-mix(in srgb, var(--color-primary), transparent 70%);
}

.btn-secondary-gray {
    background-color: #6c757d;
}

.btn-secondary-gray:hover,
.btn-secondary-gray:focus {
    background-color: #5c636a;
}

.bg-peach {
    background-color: rgb(255, 220, 220);
}

.dark .bg-peach {
    background-color: rgba(255, 220, 220, 0.7);
}

.bottom-border-dashed {
    border-bottom: 1px dashed var(--default-border) !important;
}

.bottom-border-solid {
    border-bottom: 1px solid var(--default-border) !important;
}

.border-default {
    border: 1px solid var(--default-border) !important;
}

.dark .bottom-border-dashed {
    border-bottom: 1px dashed rgba(255, 255, 255, 0.1) !important;
}

.dark .bottom-border-solid {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark .border-default {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.text-primary:hover {
    color: var(--muted-primary);
}

.text-primary:focus {
    color: var(--muted-primary
    );
}

.text-dusty-rose {
    color: var(--dusty-rose);
}

.text-dusty-rose:hover {
    color: var(--muted-rose);
}

.text-dusty-rose:focus {
    color: var(--muted-rose);
}

.bg-dusty-rose {
    background-color: var(--dusty-rose);
}

.bg-blush-pink {
    background-color: var(--blush-pink);
}

.bg-blush-pink-transparent {
    background-color: rgba(var(--blush-pink-rgb), 0.5);
}

.dark .bg-blush-pink-transparent {
    background-color: rgba(var(--blush-pink-rgb), 0.15);
}

.bg-gradient {
    background: linear-gradient(to bottom right, #8DD8FF, var(--color-primary));
    color: white;
}

.btn-unblock {
    background-color: #14b8a6;
    color: white;
}

.btn-unblock:hover {
    background-color: #0d9488;
}

.btn-unblock:focus {
    background-color: #0d9488;
    outline: none;
    box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.4);
}

.btn-unblock:active {
    background-color: #0f766e;
}

/* Dark mode */
.dark .btn-unblock {
    background-color: #2dd4bf;
    color: #042f2e;
}

.dark .btn-unblock:hover,
.dark .btn-unblock:focus {
    background-color: #14b8a6;
    color: white;
}

.dark .btn-unblock:active {
    background-color: #0d9488;
}

.alpine-tooltip {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    width: 220px;
    background-color: #334155;
    color: #ffffff;
    text-align: center;
    border-radius: 6px;
    padding: 8px 10px;
    z-index: 9999;
    font-size: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: opacity 0.2s, visibility 0.2s;
}

.alpine-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -6px;
    border-width: 6px;
    border-style: solid;
    border-color: #334155 transparent transparent transparent;
}
