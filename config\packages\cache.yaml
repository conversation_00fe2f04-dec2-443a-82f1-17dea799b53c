framework:
    cache:
        app: cache.adapter.redis
        default_redis_provider: '%env(resolve:REDIS_URL)%'
        pools:
            cache.admin_redis_limiter:
                adapter: cache.adapter.redis
                provider: '%env(resolve:REDIS_URL)%'
            cache.password_change_redis_limiter:
                adapter: cache.adapter.redis
                provider: '%env(resolve:REDIS_URL)%'
            doctrine.result_cache_pool:
                adapter: cache.adapter.redis
            doctrine.system_cache_pool:
                adapter: cache.adapter.redis
