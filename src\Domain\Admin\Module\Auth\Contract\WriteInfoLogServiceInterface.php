<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    19:26
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Contract;


use App\Domain\Admin\Module\Auth\DTO\AdminSecurityInfoLogDTO;

/**
 * Interface WriteInfoLogServiceInterface.
 *
 * This interface defines the contract for writing info logs in the admin security module.
 * It provides a method to write an info log based on the provided DTO.
 */
interface WriteInfoLogServiceInterface
{

    /**
     * Writes an info log based on the provided AdminSecurityInfoLogDTO.
     *
     * This method is responsible for persisting the log information
     * to the appropriate storage mechanism.
     *
     * @param AdminSecurityInfoLogDTO $dto The data transfer object containing the log information.
     *
     * @return bool Returns true if the log was successfully written, false otherwise.
     */
    public function write(AdminSecurityInfoLogDTO $dto): bool;

}
