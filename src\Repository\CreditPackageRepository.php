<?php

declare(strict_types=1);

namespace App\Repository;

use App\Base\BaseRepository;
use App\Entity\CreditPackage;

/**
 * Class CreditPackageModelRepository.
 *
 * This class is responsible for handling credit-package-related database operations.
 */
class CreditPackageRepository extends BaseRepository
{

    protected function loadEntity(): string
    {
        return CreditPackage::class;
    }

}
