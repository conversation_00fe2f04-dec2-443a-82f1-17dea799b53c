<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/18/2025
 * @time 7:00 PM
 */

namespace App\Domain\Admin\Module\AccountManagement\Contract;

use App\Domain\Admin\Module\AccountManagement\DTO\UserStatus\UserStatusRequestDTO;

/**
 * Interface UserStatusServiceInterface
 *
 * Defines the contract for user status management actions within the Account Management module.
 */
interface UserStatusServiceInterface
{

    /**
     * Activates a user account.
     *
     * @param  UserStatusRequestDTO  $userStatusRequestDTO  The data transfer object containing the user ID.
     *
     * @return bool Returns true if the user was successfully activated, false otherwise.
     */
    public function activateUser(UserStatusRequestDTO $userStatusRequestDTO): bool;

    /**
     * Deactivates a user account.
     *
     * @param  UserStatusRequestDTO  $userStatusRequestDTO  The data transfer object containing the user ID.
     *
     * @return bool Returns true if the user was successfully deactivated, false otherwise.
     */
    public function deactivateUser(UserStatusRequestDTO $userStatusRequestDTO): bool;

    /**
     * Restores a previously deleted user account if it's within the restorable period.
     *
     * @param  UserStatusRequestDTO  $userStatusRequestDTO  The data transfer object containing the user ID.
     *
     * @return bool Returns true if the user was successfully restored, false otherwise.
     */
    public function restoreUser(UserStatusRequestDTO $userStatusRequestDTO): bool;

}
