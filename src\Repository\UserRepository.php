<?php

declare(strict_types=1);

namespace App\Repository;

use App\Base\BaseRepository;
use App\Entity\Constant\UserConstant;
use App\Entity\User;
use DateTimeImmutable;

/**
 * Class UserRepository.
 *
 * This class is responsible for handling user-related database operations.
 */
class UserRepository extends BaseRepository
{

    /**
     *
     * @param  int  $parentId  The ID of the parent user.
     *
     * @return array Returns an array of restorable descendant user entities.
     */
    public function findRestorableDescendants(int $parentId, \DateTimeImmutable $threshold): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.metaData.parentId = :parentId')
            ->andWhere('u.timestamps.deletedAt IS NOT NULL')
            ->andWhere('u.timestamps.deletedAt >= :threshold')
            ->setParameter('parentId', $parentId)
            ->setParameter('threshold', $threshold)
            ->getQuery()
            ->getResult();
    }

    protected function loadEntity(): string
    {
        return User::class;
    }

}
