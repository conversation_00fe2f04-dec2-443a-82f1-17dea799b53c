<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    00:58
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Contract;

use App\Entity\User;

/**
 * Interface ChangePasswordServiceInterface.
 *
 * This interface defines the contract for changing a user's password.
 * It provides a method to change the password for a user identified by their ID.
 */
interface ChangePasswordServiceInterface
{

    /**
     * Change the password for a user.
     *
     * @param User $user The user entity for which the password is to be changed.
     * @param string $password The new password to set for the user.
     *
     * @return bool Returns true if the password was successfully changed, false otherwise.
     */
    public function changePassword(User $user, string $password): bool;

    /**
     * Verify the current password and change it to a new password.
     *
     * @param User $user The user entity for which the password is to be changed.
     * @param string|null $currentPassword The current password to verify.
     * @param string|null $newPassword The new password to set for the user.
     *
     * @return bool Returns true if the password was successfully changed, false otherwise.
     */
    public function verifyAndChangePassword(User $user, ?string $currentPassword, ?string $newPassword): bool;

}
