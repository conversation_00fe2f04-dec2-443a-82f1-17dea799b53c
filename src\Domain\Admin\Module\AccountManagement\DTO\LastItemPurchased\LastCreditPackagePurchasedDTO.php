<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    15:47
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO\LastItemPurchased;

/**
 * Data Transfer Object (DTO) for Last Credit Package Purchased (LIP) information.
 *
 * This DTO represents the details of a purchased credit package, typically used
 * for displaying information about the last purchased item in an administrative
 * or user interface context.
 */
final class LastCreditPackagePurchasedDTO
{
    public ?string $itemName;
    public ?string $itemType;
    public ?string $shortcut;
    public ?string $name;
    public ?string $description;
    public string|float|null $price;
    public string|float|null $credit;
}
