name: Pull Request Validation

# Add permissions configuration for GITHUB_TOKEN
permissions:
    pull-requests: write
    issues: write

on:
    pull_request:
        types: [ opened, synchronize, reopened ]
        branches:
            - staging
            - main

jobs:
    check-exempted-users:
        runs-on: ubuntu-latest
        outputs:
            is_exempted: ${{ steps.check-user.outputs.IS_EXEMPTED }}
        steps:
            -   name: Check if user is exempted
                id: check-user
                run: |
                    if [[ "${{ github.actor }}" == "evo-master" || "${{ github.actor }}" == "m397dev" ]]; then
                      echo "IS_EXEMPTED=true" >> $GITHUB_OUTPUT
                      echo "::notice::User ${{ github.actor }} is exempted from PR validation checks."
                    else
                      echo "IS_EXEMPTED=false" >> $GITHUB_OUTPUT
                    fi

    validate-pr-source:
        runs-on: ubuntu-latest
        # [THAY ĐỔI] - Thêm dependency và điều kiện kiểm tra
        needs: check-exempted-users
        if: needs.check-exempted-users.outputs.is_exempted != 'true'
        steps:
            -   name: Check PR to main
                if: github.event.pull_request.base.ref == 'main'
                id: check-pr-main
                run: |
                    if [[ "${{ github.event.pull_request.head.ref }}" != "staging" ]]; then
                      echo "VIOLATION=true" >> $GITHUB_OUTPUT
                      echo "::warning::Pull Requests to main must come from staging branch. Current source: ${{ github.event.pull_request.head.ref }}"
                      exit 1
                    fi
                    echo "Valid PR: staging -> main"

            -   name: Check PR to staging
                if: github.event.pull_request.base.ref == 'staging'
                id: check-pr-staging
                run: |
                    if [[ "${{ github.event.pull_request.head.ref }}" == "main" ]]; then
                      echo "VIOLATION=true" >> $GITHUB_OUTPUT
                      echo "::warning::Pull Requests from main to staging are not allowed."
                      exit 1
                    fi
                    echo "Valid PR: feature branch -> staging"

            # Add warning comment to PR targeting main
            -   name: Comment on PR for main violation
                if: failure() && steps.check-pr-main.outputs.VIOLATION == 'true'
                uses: actions/github-script@v6
                with:
                    github-token: ${{ secrets.GITHUB_TOKEN }}
                    script: |
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          body: `
                          ## ⚠️ Pull Request Rule Violation

                          Pull Requests to main branch must come from staging branch.

                          - Current target branch: main
                          - Current source branch: ${{ github.event.pull_request.head.ref }}

                          Please create a new Pull Request from staging to main.
                          `
                        });

            # Add warning comment to PR from main to staging
            -   name: Comment on PR for staging violation
                if: failure() && steps.check-pr-staging.outputs.VIOLATION == 'true'
                uses: actions/github-script@v6
                with:
                    github-token: ${{ secrets.GITHUB_TOKEN }}
                    script: |
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          body: `
                          ## ⚠️ Pull Request Rule Violation

                          Pull Requests from main branch to staging are not allowed.

                          - Current target branch: staging
                          - Current source branch: main

                          Please create Pull Requests from feature branches to staging.
                          `
                        });

            # Check if Slack webhook is configured
            -   name: Check Slack webhook
                id: check-slack
                run: |
                    if [[ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]]; then
                      echo "HAS_SLACK_WEBHOOK=true" >> $GITHUB_OUTPUT
                    else
                      echo "HAS_SLACK_WEBHOOK=false" >> $GITHUB_OUTPUT
                    fi

            # Add warning label to violating PR
            -   name: Add warning label to PR
                if: failure() && (steps.check-pr-main.outputs.VIOLATION == 'true' || steps.check-pr-staging.outputs.VIOLATION == 'true')
                uses: actions/github-script@v6
                with:
                    github-token: ${{ secrets.GITHUB_TOKEN }}
                    script: |
                        await github.rest.issues.addLabels({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          labels: ['branch-protection-violation']
                        });

            # Send Slack notification only if webhook is configured
            -   name: Send Slack notification for PR violation
                if: failure() && (steps.check-pr-main.outputs.VIOLATION == 'true' || steps.check-pr-staging.outputs.VIOLATION == 'true') && steps.check-slack.outputs.HAS_SLACK_WEBHOOK == 'true'
                uses: 8398a7/action-slack@v3
                with:
                    status: custom
                    fields: repo,message,commit,author
                    custom_payload: |
                        {
                          "attachments": [
                            {
                              "color": "#FF0000",
                              "title": "❌ Pull Request Rule Violation",
                              "text": "Invalid Pull Request detected in ${{ github.repository }} by @${{ github.actor }}.\nPR #${{ github.event.pull_request.number }}: ${{ github.event.pull_request.title }}",
                              "footer": "GitHub Actions",
                              "footer_icon": "https://github.githubassets.com/favicon.ico"
                            }
                          ]
                        }
                env:
                    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
                continue-on-error: true
