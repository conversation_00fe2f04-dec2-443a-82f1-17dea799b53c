<?php

declare(strict_types=1);

namespace App\Domain\Portal\Module\LandingPage\Controller;

use App\Domain\Portal\Base\PortalController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class LandingPageController.
 *
 * This is the default controller of this application, handles the landing-page and related functionalities.
 */
#[Route('/', name: 'app_portal_landing_page_')]
class LandingPageController extends PortalController
{

    /**
     * Action `index`.
     *
     * This is the default action of this module, handles the landing-page request.
     */
    #[Route(name: 'index', methods: ['GET'])]
    public function index(): Response
    {
        return $this->view('landing-page/pages/index');
    }

}
