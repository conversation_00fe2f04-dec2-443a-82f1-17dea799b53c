<?php

declare(strict_types=1);

namespace App\Library\ReCaptcha\Contract;

/**
 * Interface ReCaptchaServiceInterface.
 *
 * This interface defines the contract for a reCAPTCHA service.
 *
 * It is responsible for handling reCAPTCHA verification and related operations.
 */
interface ReCaptchaServiceInterface
{

    /**
     * Get the site key for ReCaptcha.
     *
     * @return string|null
     */
    public function getSiteKey(): ?string;

    /**
     * Get the secret key for ReCaptcha.
     *
     * @return string|null
     */
    public function getSecretKey(): ?string;

    /**
     * Get the threshold for ReCaptcha.
     *
     * @return float
     */
    public function getThreshold(): float;

    /**
     * Get the hostname for ReCaptcha.
     *
     * @return string|null
     */
    public function getHostname(): ?string;

    /**
     * Returns the ReCaptcha javascript.
     *
     * This method returns the javascript code for the reCAPTCHA service.
     *
     * @return string|null
     */
    public function getScript(): ?string;

    /**
     * Validate the captcha response.
     *
     * This method validates the captcha response using the ReCaptcha service.
     *
     * @param  string  $response  The captcha response to validate
     * @param  array   $data      Additional data for validation
     *
     * @return bool
     */
    public function validate(string $response, array $data = []): bool;

}
