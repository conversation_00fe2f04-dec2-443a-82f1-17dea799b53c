<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class UserWalletTransactionConstant.
 *
 * This class holds all constant values related to the UserWalletTransaction entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing user-wallet-transaction-specific data.
 */
final class UserWalletTransactionConstant
{

    final public const int TYPE_INCREASE = 0;

    final public const int TYPE_DECREASE = 1;

    final public const array TYPES
        = [
            self::TYPE_INCREASE => 'Increase',
            self::TYPE_DECREASE => 'Decrease',
        ];

}
