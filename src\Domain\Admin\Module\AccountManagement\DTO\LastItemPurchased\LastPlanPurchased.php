<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    15:44
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO\LastItemPurchased;

/**
 * Data Transfer Object (DTO) for Last Plan Purchased (LIP) information.
 *
 * This DTO encapsulates the essential details of a purchased plan, typically used
 * for displaying a summary or specific attributes of the last purchased plan
 * within an administrative dashboard or user interface.
 */
final class LastPlanPurchased
{
    public ?string $itemName;
    public ?string $itemType;
    public ?string $shortcut;
    public ?string $name;
    public ?string $description;
    public ?int $type;
    public string|float|null $price;
    public ?string $length;
}
