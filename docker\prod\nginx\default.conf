server {
    listen 80;
    server_name ${DOCKER_WEB_HOST};

    # Redirect to HTTPS in production
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name ${DOCKER_WEB_HOST};

    root /var/www/zenshop/public;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/ssl.pem;
    ssl_certificate_key /etc/nginx/ssl/ssl.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 10m;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # For Cloudflare SSL compatibility
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from 2400:cb00::/32;
    set_real_ip_from 2606:4700::/32;
    set_real_ip_from 2803:f800::/32;
    set_real_ip_from 2405:b500::/32;
    set_real_ip_from 2405:8100::/32;
    set_real_ip_from 2a06:98c0::/29;
    set_real_ip_from 2c0f:f248::/32;

    real_ip_header CF-Connecting-IP;

    location / {
        # try to serve file directly, fallback to index.php
        try_files $uri /index.php$is_args$args;
    }

    # Equivalent to the Authorization header handling in .htaccess
    location ~ ^/index\.php(/|$) {
        fastcgi_pass app:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;

        # Set Authorization header from HTTP_AUTHORIZATION if present
        fastcgi_param HTTP_AUTHORIZATION $http_authorization if_not_empty;

        # Tăng timeout cho các yêu cầu lâu hơn
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;

        internal;
    }

    # Return 404 for all other php files not matching the front controller
    location ~ \.php$ {
        return 404;
    }

    # Deny access to .htaccess files
    location ~ /\.ht {
        deny all;
    }

    # Enable browser caching for static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
    }

    error_log /var/log/nginx/zenshop_error.log;
    access_log /var/log/nginx/zenshop_access.log;

    include mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    server_tokens off;
    client_max_body_size 2M;
    client_body_buffer_size 2M;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
}
