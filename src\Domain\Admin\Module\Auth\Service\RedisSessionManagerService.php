<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    14:41
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Service;

use App\Domain\Admin\Module\Auth\Contract\SessionManagerServiceInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Redis;

/**
 * Class RedisSessionManagerService.
 *
 * This service manages user sessions in a Redis database.
 * It provides functionality to invalidate other sessions for a specific user.
 * It uses the Redis client to interact with the session data stored in Redis.
 */
class RedisSessionManagerService implements SessionManagerServiceInterface
{

    private Redis $redis;
    private LoggerInterface $logger;

    public function __construct(
        Redis $redis,
        LoggerInterface $logger,
    ) {
        $this->redis = $redis;
        $this->logger = $logger;
    }

    /**
     * @inheritDoc
     */
    public function invalidateOtherSessions(int|string $userId, string $currentSessionId): bool
    {
        try {
            $sessionKeyPattern = 'PHPREDIS_SESSION:*';
            $keys = $this->redis->keys($sessionKeyPattern);

            foreach ($keys as $key) {
                $sessionId = str_replace('PHPREDIS_SESSION:', '', $key);

                if ($sessionId === $currentSessionId) {
                    continue;
                }

                $sessionData = $this->redis->get($key);
                if (!$sessionData) {
                    continue;
                }

                if (str_contains($sessionData, 'admin_context') &&
                    (str_contains($sessionData, sprintf('i:%d', $userId)) ||
                        str_contains($sessionData, sprintf('s:%d:"%s"', strlen((string)$userId), $userId)))) {
                    $this->redis->del($key);
                }
            }

            return true;
        } catch (Exception $e) {
            $this->logger->error('Error when logging out other sessions: '.$e->getMessage());

            return false;
        }
    }

}
