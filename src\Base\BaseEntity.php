<?php

declare(strict_types=1);

namespace App\Base;

use Doctrine\ORM\Mapping as ORM;

/**
 * Abstract class BaseEntity.
 *
 * This is the base class for all entities in the system.
 *
 * Derived classes should implement the specific logic and properties for the respective entities.
 * This class is meant to provide common fields and behavior to ensure consistency across all entities.
 *
 * Commonly used by Doctrine ORM for database mapping.
 */
abstract class BaseEntity
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(name: 'id', type: 'bigint')]
    protected ?int $id = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): static
    {
        if ($this->id === null) {
            $this->id = $id;
        }

        return $this;
    }

}
