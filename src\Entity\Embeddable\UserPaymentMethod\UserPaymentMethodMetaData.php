<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\UserPaymentMethod;

use App\Entity\Constant\UserPaymentMethodConstant;
use App\Entity\Trait\HasStatusTrait;
use App\Entity\Trait\HasTypeTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserPaymentMethodMetaData.
 *
 * This class represents meta-data about a user-payment-method.
 *
 * It is used as an embeddable entity in the UserPaymentMethod entity.
 */
#[ORM\Embeddable]
final class UserPaymentMethodMetaData
{

    use HasTypeTrait;
    use HasStatusTrait;

    #[ORM\Column(name: 'data_key', length: 64, unique: true)]
    private readonly string $dataKey;

    #[ORM\Column(name: 'data_value', type: 'json')]
    private readonly array $dataValue;

    #[ORM\Column(name: 'is_default', type: 'smallint', options: ['default' => UserPaymentMethodConstant::IS_DEFAULT_NO])]
    private int $isDefault = UserPaymentMethodConstant::IS_DEFAULT_NO;

    public function __construct(string $dataKey, array $dataValue)
    {
        $this->dataKey = $dataKey;
        $this->dataValue = $dataValue;
    }

    public function getDataKey(): string
    {
        return $this->dataKey;
    }

    public function getDataValue(): array
    {
        return $this->dataValue;
    }

    public function getIsDefault(): int
    {
        return $this->isDefault;
    }

    public function setIsDefault(int $isDefault = UserPaymentMethodConstant::IS_DEFAULT_NO): UserPaymentMethodMetaData
    {
        $this->isDefault = $isDefault;

        return $this;
    }

}
