<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    05/06/2025
 * @time    15:42
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Data Transfer Object (DTO) for user wallet transaction information.
 *
 * This DTO represents a single transaction affecting a user's wallet,
 * providing details about the amount, resulting credit balance, transaction type,
 * additional JSON data, and the timestamp of the event. It is typically used
 * for displaying transaction history or auditing wallet activities.
 */
final class UserWalletTransactionDTO
{
    public string|float|null $amount;
    public string|float|null $credit;
    public ?int $type;
    public ?array $jsonData;
    public ?string $loggedAt;
}
