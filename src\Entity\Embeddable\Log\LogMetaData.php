<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Log;

use App\Entity\Constant\LogConstant;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class LogMetaData.
 *
 * This class represents the content of a log level, entry, including the message,
 * context, and extra information.
 */
#[ORM\Embeddable]
class LogMetaData
{

    #[ORM\Column(type: Types::INTEGER)]
    private int $level;

    #[ORM\Column(type: Types::STRING, length: 255)]
    private string $levelName;

    #[ORM\Column(type: Types::TEXT)]
    private string $message;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $context = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $extra = null;

    public function getLevel(): int
    {
        return $this->level;
    }

    public function setLevel(int $level): LogMetaData
    {
        $this->level = $level;
        $this->levelName = LogConstant::LOG_LEVELS[$level];
        return $this;
    }

    public function getLevelName(): string
    {
        return $this->levelName;
    }

    public function setLevelName(string $levelName): LogMetaData
    {
        $this->levelName = $levelName;
        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): LogMetaData
    {
        $this->message = $message;
        return $this;
    }

    public function getContext(): ?array
    {
        return $this->context;
    }

    public function setContext(?array $context): LogMetaData
    {
        $this->context = $context;
        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): LogMetaData
    {
        $this->extra = $extra;
        return $this;
    }

}
