<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\UserProfile\UserProfileMetaData;
use App\Repository\UserProfileRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserProfile.
 *
 * Represents a user-profile in the system. Maps to the `user_profiles` table in the database.
 */
#[ORM\Entity(repositoryClass: UserProfileRepository::class)]
#[ORM\Table(name: 'user_profiles')]
class UserProfile extends BaseEntity
{

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: 'userProfile')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    private User $user;

    #[ORM\Embedded(class: UserProfileMetaData::class, columnPrefix: false)]
    private UserProfileMetaData $metaData;

    public function __construct()
    {
        $this->metaData = new UserProfileMetaData();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function loadMetaData(): UserProfileMetaData
    {
        return $this->metaData;
    }

}
