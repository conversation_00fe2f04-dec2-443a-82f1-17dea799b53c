<?php

declare(strict_types=1);

namespace App\Utils\Helper;

use Random\RandomException;

/**
 * Class StringHelper.
 *
 * This class is used to provide helper methods for working with strings.
 */
class StringHelper
{

    /**
     * Generates a random string of a specified length using characters from a predefined set.
     *
     * @param  int  $length  The length of the random string to generate. Default is 8.
     *
     * @return string A random string composed of letters from the English alphabet.
     *
     * @throws RandomException
     */
    public static function randomString(int $length = 8): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charLength = strlen($characters);
        $result = '';

        while (strlen($result) < $length) {
            $randomBytes = random_bytes($length);

            foreach (str_split($randomBytes) as $byte) {
                $index = ord($byte) % $charLength;
                $result .= $characters[$index];

                if (strlen($result) >= $length) {
                    break;
                }
            }
        }

        return $result;
    }

}
