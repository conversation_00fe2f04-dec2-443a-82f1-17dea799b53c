<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    16:06
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Service;

use App\Domain\Admin\Module\AccountManagement\Contract\GetUserAccountDetailServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\AccountInformationDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\LastItemPurchased\LastCreditPackagePurchasedDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserAccountDetailDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserProfileDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserTypeInfo\ClientDataDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserWalletDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserWalletTransactionDTO;
use App\Entity\Constant\UserConstant;
use App\Entity\CreditPackage;
use App\Entity\User;
use App\Repository\ClientRepository;
use App\Repository\CreditPackageRepository;
use App\Repository\UserProfileRepository;
use App\Repository\UserRepository;
use App\Repository\UserWalletRepository;
use App\Repository\UserWalletTransactionRepository;

/**
 * Class GetUserAccountDetailService.
 *
 * This class is responsible for fetching detailed information about a user account.
 * It coordinates with various repositories to gather data and maps it to a DTO for
 * easy consumption by the presentation layer.
 */
class GetUserAccountDetailService implements GetUserAccountDetailServiceInterface
{
    private UserRepository $userRepository;
    private UserProfileRepository $userProfileRepository;
    private UserWalletRepository $userWalletRepository;
    private UserWalletTransactionRepository $userWalletTransactionRepository;
    private ClientRepository $clientRepository;
    private CreditPackageRepository $creditPackageRepository;

    public function __construct(
        UserRepository $userRepository,
        UserProfileRepository $userProfileRepository,
        UserWalletRepository $userWalletRepository,
        UserWalletTransactionRepository $userWalletTransactionRepository,
        ClientRepository $clientRepository,
        CreditPackageRepository $creditPackageRepository
    ) {
        $this->userRepository = $userRepository;
        $this->userProfileRepository = $userProfileRepository;
        $this->userWalletRepository = $userWalletRepository;
        $this->userWalletTransactionRepository = $userWalletTransactionRepository;
        $this->clientRepository = $clientRepository;
        $this->creditPackageRepository = $creditPackageRepository;
    }

    /**
     * @inheritDoc
     */
    public function getSummary(int $id): ?UserAccountDetailDTO
    {
        $userAccountDetailDTO = new UserAccountDetailDTO();
        $userAccountDetailDTO->accountInformationDTO = $this->getAccountInformation($id);

        if ($userAccountDetailDTO->accountInformationDTO === null
            || in_array(UserConstant::ROLE_ADMIN, $userAccountDetailDTO->accountInformationDTO->roles, true)) {
            return null;
        }

        $userAccountDetailDTO->userProfileDTO = $this->getUserProfile($id);
        $userAccountDetailDTO->userWalletDTO = $this->getUserWallet($id);

        if ($userAccountDetailDTO->userWalletDTO !== null) {
            $userAccountDetailDTO->userWalletTransactionsDTO = $this->getUserWalletTransactions(
                $this->userWalletRepository->findOneBy(['user' => (string)$id])?->getId()
            );
        }

        switch ($userAccountDetailDTO->accountInformationDTO->roles[0]):
            case UserConstant::ROLE_CLIENT:
                $userAccountDetailDTO->userTypeSpecificData = $this->getClientData($id);
                $userAccountDetailDTO->lastItemPurchasedDTO = $this->getLastCreditPackagePurchasedData(
                    $this->clientRepository->findOneBy(['user' => (string)$id])?->getCreditPackage()?->getId()
                );
                break;

            case UserConstant::ROLE_RESELLER:
                $userAccountDetailDTO->userTypeSpecificData = $this->getResellerData();
                $userAccountDetailDTO->lastItemPurchasedDTO = $this->getLastPlanPurchasedData();
                break;

            case UserConstant::ROLE_PARTNER:
                $userAccountDetailDTO->userTypeSpecificData = $this->getPartnerData();
                $userAccountDetailDTO->lastItemPurchasedDTO = $this->getLastProgramPurchasedData();
                break;

            default:
                break;

        endswitch;

        return $userAccountDetailDTO;
    }

    /**
     * @inheritDoc
     */
    public function getAccountInformation(int $id): ?AccountInformationDTO
    {
        $this->userRepository->getManager()->getFilters()->disable('softdeleteable');
        $userAccountEntity = $this->userRepository->find($id);

        if (!$userAccountEntity instanceof User) {
            return null;
        }

        $result = new AccountInformationDTO();

        $result->parentId = $userAccountEntity->loadMetaData()->getParentId();
        $result->username = $userAccountEntity->loadMetaData()->getUsername();
        $result->email = $userAccountEntity->loadCredentials()->getEmail();
        $result->roles = $userAccountEntity->loadCredentials()->getRoles();
        $result->registrationIp = $userAccountEntity->loadMetaData()->getRegistrationIp();
        $result->status = $userAccountEntity->loadMetaData()->getStatus();
        $result->lastLoginAt = $userAccountEntity->loadTimestamps()->getLastLoginAt()?->format(DATE_ATOM);
        $result->blockedAt = $userAccountEntity->loadTimestamps()->getBlockedAt()?->format(DATE_ATOM);
        $result->deletedAt = $userAccountEntity->loadTimestamps()->getDeletedAt()?->format(DATE_ATOM);
        $result->createdAt = $userAccountEntity->getCreatedAt()->format(DATE_ATOM);
        $result->updatedAt = $userAccountEntity->getUpdatedAt()->format(DATE_ATOM);

        if ($result->parentId !== null) {
            $result->parent = $this->userRepository->find($result->parentId)?->loadMetaData()?->getUsername();
        } else {
            $result->parent = "";
        }

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getUserProfile(int $userId): ?UserProfileDTO
    {
        $userProfileEntity = $this->userProfileRepository->findOneBy(['user' => $userId]);

        if ($userProfileEntity === null) {
            return null;
        }

        $result = new UserProfileDTO();

        $result->avatar = $userProfileEntity->loadMetaData()->getAvatar();
        $result->firstName = $userProfileEntity->loadMetaData()->getFirstName();
        $result->lastName = $userProfileEntity->loadMetaData()->getLastName();
        $result->dob = $userProfileEntity->loadMetaData()->getDob();
        $result->biography = $userProfileEntity->loadMetaData()->getBiography();
        $result->gender = $userProfileEntity->loadMetaData()->getGender();

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getUserWallet(int $userId): ?UserWalletDTO
    {
        $userWalletEntity = $this->userWalletRepository->findOneBy(['user' => $userId]);

        if ($userWalletEntity === null) {
            return null;
        }

        $result = new UserWalletDTO();
        $result->credit = $userWalletEntity->loadMetaData()->getCredit();
        $result->createdAt = $userWalletEntity->getCreatedAt()->format(DATE_ATOM);
        $result->updatedAt = $userWalletEntity->getUpdatedAt()->format(DATE_ATOM);

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getUserWalletTransactions(int|null $userWalletId): ?array
    {
        if ($userWalletId === null) {
            return null;
        }

        $userWalletTransactions = $this->userWalletTransactionRepository->findBy(['userWallet' => $userWalletId]);

        if (empty($userWalletTransactions)) {
            return null;
        }

        $result = [];

        foreach ($userWalletTransactions as $userWalletTransaction) {
            $dto = new UserWalletTransactionDTO();

            $dto->amount = $userWalletTransaction->loadMetaData()->getAmount();
            $dto->credit = $userWalletTransaction->loadMetaData()->getCredit();
            $dto->type = $userWalletTransaction->loadMetaData()->getType();
            $dto->jsonData = $userWalletTransaction->loadMetaData()->getJsonData();
            $dto->loggedAt = $userWalletTransaction->loadTimestamps()->getLoggedAt()->format(DATE_ATOM);

            $result[] = $dto;
        }

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getClientData(int $id): ?ClientDataDTO
    {
        $clientEntity = $this->clientRepository->findOneBy(['user' => $id]);

        if ($clientEntity === null) {
            return null;
        }

        $result = new ClientDataDTO();

        $result->isAutoRenew = $clientEntity->loadMetaData()->getIsAutoRenew();
        $result->lastRenewAt = $clientEntity->loadTimestamps()->getLastRenewAt()?->format(DATE_ATOM);

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getResellerData(): null
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getPartnerData(): null
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getLastPlanPurchasedData(): null
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getLastCreditPackagePurchasedData(int|string|null $id): ?LastCreditPackagePurchasedDTO
    {
        if ($id === null) {
            return null;
        }

        $creditPackageEntity = $this->creditPackageRepository->find($id);

        if (!$creditPackageEntity instanceof CreditPackage) {
            return null;
        }

        $result = new LastCreditPackagePurchasedDTO();

        $result->shortcut = $creditPackageEntity->loadMetaData()->getShortcut();
        $result->name = $creditPackageEntity->loadMetaData()->getName();
        $result->description = $creditPackageEntity->loadMetaData()->getDescription();
        $result->price = $creditPackageEntity->loadMetaData()->getPrice();
        $result->credit = $creditPackageEntity->loadMetaData()->getCredit();
        $result->itemName = $creditPackageEntity->loadMetaData()->getName();
        $result->itemType = 'Credit Package';

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function getLastProgramPurchasedData(): null
    {
        return null;
    }

}
