<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\UserPaymentMethod\UserPaymentMethodMetaData;
use App\Entity\Trait\HasLifeCycleTrait;
use App\Repository\UserPaymentMethodRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserPaymentMethod.
 *
 * Represents a user's payment method in the system. Maps to the `user_payment_methods` table in the database.
 */
#[ORM\Entity(repositoryClass: UserPaymentMethodRepository::class)]
#[ORM\Table(name: 'user_payment_methods')]
#[ORM\HasLifecycleCallbacks]
class UserPaymentMethod extends BaseEntity
{

    use HasLifeCycleTrait;

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: 'userPaymentMethods')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    private User $user;

    #[ORM\Embedded(class: UserPaymentMethodMetaData::class, columnPrefix: false)]
    private UserPaymentMethodMetaData $metaData;

    public function __construct(string $dataKey, array $dataValue)
    {
        $this->metaData = new UserPaymentMethodMetaData($dataKey, $dataValue);
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function loadMetaData(): UserPaymentMethodMetaData
    {
        return $this->metaData;
    }

}
