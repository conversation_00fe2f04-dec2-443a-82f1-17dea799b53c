<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\User;

use App\Entity\Trait\HasStatusTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserMetaData.
 *
 * This class represents meta-data about a user.
 *
 * It is used as an embeddable entity in the User entity.
 */
#[ORM\Embeddable]
final class UserMetaData
{

    use HasStatusTrait;

    #[ORM\Column(name: 'parent_id', type: 'bigint', nullable: true, options: ['default' => null])]
    private ?int $parentId = null;

    #[ORM\Column(name: 'username', length: 100)]
    private string $username;

    #[ORM\Column(name: 'registration_ip', type: 'string', length: 45, nullable: true, options: ['default' => null])]
    private ?string $registrationIp = null;

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function setParentId(?int $parentId): UserMetaData
    {
        $this->parentId = $parentId;

        return $this;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function setUsername(string $username): UserMetaData
    {
        $this->username = $username;

        return $this;
    }

    public function getRegistrationIp(): ?string
    {
        return $this->registrationIp;
    }

    public function setRegistrationIp(?string $registrationIp): UserMetaData
    {
        $this->registrationIp = $registrationIp;

        return $this;
    }

}
