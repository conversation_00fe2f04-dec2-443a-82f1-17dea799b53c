<?php

declare(strict_types=1);

namespace App\Utils\Helper;

use Exception;

/**
 * Class NetworkHelper.
 *
 * A utility class that provides helper methods for network-related operations.
 */
class NetworkHelper
{

    /**
     * Retrieves the real IP address of the client.
     *
     * This method checks for the presence of various headers that may contain the client's IP address,
     * such as 'HTTP_CF_CONNECTING_IP' and 'HTTP_X_FORWARDED_FOR', and returns the first valid IP found.
     *
     * @return string|null The real IP address of the client, or null if not found.
     */
    public static function realIP(): ?string
    {
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
            $ips = explode(',', $ipaddress);
            $ipaddress = trim($ips[0]);
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipaddress = 'UNKNOWN';
        }

        return $ipaddress;
    }

    /**
     * Fetches location data based on the provided IP address using ip-api.com.
     *
     * @param  string  $ip  The IP address to look up.
     *
     * @return array An associative array containing location data.
     */
    public static function locationIpApi(string $ip): array
    {
        try {
            $ipData = json_decode(file_get_contents('http://ip-api.com/json/'.$ip), false, 512, JSON_THROW_ON_ERROR);

            if (isset($ipData['status']) && $ipData['status'] === 'success') {
                $response = [];
                $response['city'] = $ipData['city'];
                $response['state'] = $ipData['regionName'];
                $response['country'] = $ipData['country'];
                $response['country_code'] = $ipData['countryCode'];
                $response['continent'] = $ipData['regionName'];
                $response['continent_code'] = $ipData['region'];

                return $response;
            }
        } catch (Exception $e) {
            echo 'Error fetching IP data: '.$e->getMessage();
        }

        return [
            'city'           => 'Local',
            'state'          => 'Local',
            'country'        => 'Local',
            'country_code'   => 'Local',
            'continent'      => 'Local',
            'continent_code' => 'Local',
        ];
    }

    /**
     * Fetches location data based on the provided IP address using GeoPlugin.
     *
     * @param  string  $ip  The IP address to look up.
     *
     * @return array An associative array containing location data.
     */
    public static function locationGeoPlugin(string $ip): array
    {
        try {
            $ipData = json_decode(
                file_get_contents('http://www.geoplugin.net/json.gp?ip='.$ip),
                false,
                512,
                JSON_THROW_ON_ERROR
            );

            if (isset($ipData['geoplugin_status']) && $ipData['geoplugin_status'] !== 404) {
                $response = [];
                $response['city'] = $ipData['geoplugin_city'];
                $response['state'] = $ipData['geoplugin_regionName'];
                $response['country'] = $ipData['geoplugin_countryName'];
                $response['country_code'] = $ipData['geoplugin_countryCode'];
                $response['continent'] = $ipData['geoplugin_continentName'];
                $response['continent_code'] = $ipData['geoplugin_continentCode'];

                return $response;
            }
        } catch (Exception $e) {
            echo 'Error fetching GeoPlugin data: '.$e->getMessage();
        }

        return [
            'city'           => 'Local',
            'state'          => 'Local',
            'country'        => 'Local',
            'country_code'   => 'Local',
            'continent'      => 'Local',
            'continent_code' => 'Local',
        ];
    }

    /**
     * Extracts the bottom-level domain from a referrer URL.
     *
     * @param  string  $referrer  The referrer URL to process.
     *
     * @return string|null The bottom-level domain or path, or null if the referrer is empty or invalid.
     */
    public static function clearReferrer(string $referrer): ?string
    {
        $bottomHostName = null;

        if ($referrer !== '' && ($parse = parse_url($referrer)) !== null) {
            if (isset($parse['scheme'], $parse['host'])) {
                if (str_contains($parse['scheme'], 'http')) {
                    $hostNames = array_filter(explode(".", $parse['host']));
                    $bottomHostName = $hostNames[count($hostNames) - 2].".".$hostNames[count($hostNames) - 1];
                } else {
                    $bottomHostName = $parse['scheme'].'://'.$parse['host'];
                }
            } else {
                $bottomHostName = $parse['path'];
            }
        }

        return $bottomHostName;
    }

}
