<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    14:40
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Contract;

/**
 * Interface SessionManagerServiceInterface.
 *
 * This interface defines the contract for managing user sessions, specifically
 * for invalidating other sessions of a user when a new session is created.
 * It is used to ensure that only one active session per user is allowed,
 * which is a common security measure to prevent session hijacking or unauthorized access.
 */
interface SessionManagerServiceInterface
{

    /**
     * Invalidate all other sessions for a user except the current session.
     *
     * This method is typically called when a new session is created for a user,
     * ensuring that any previous sessions are invalidated to maintain security.
     *
     * @param int|string $userId The ID of the user whose sessions are to be invalidated.
     * @param string $currentSessionId The ID of the current session that should not be invalidated.
     *
     * @return bool Returns true if the operation was successful, false otherwise.
     */
    public function invalidateOtherSessions(int|string $userId, string $currentSessionId): bool;

}
