<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\Client\ClientMetaData;
use App\Entity\Embeddable\Client\ClientTimestamps;
use App\Entity\Trait\HasLifeCycleTrait;
use App\Repository\ClientRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Client.
 *
 * Represents a client in the system. Maps to the `clients` table in the database.
 */
#[ORM\Entity(repositoryClass: ClientRepository::class)]
#[ORM\Table(name: 'clients')]
#[ORM\HasLifecycleCallbacks]
class Client extends BaseEntity
{

    use HasLifeCycleTrait;

    #[ORM\OneToOne(targetEntity: User::class, inversedBy: 'client')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', onDelete: 'RESTRICT')]
    private User $user;

    #[ORM\ManyToOne(targetEntity: CreditPackage::class, inversedBy: 'clients')]
    #[ORM\JoinColumn(name: 'credit_package_id', referencedColumnName: 'id', nullable: true, onDelete: 'SET NULL')]
    private ?CreditPackage $creditPackage;

    #[ORM\Embedded(class: ClientMetaData::class, columnPrefix: false)]
    private ClientMetaData $metaData;

    #[ORM\Embedded(class: ClientTimestamps::class, columnPrefix: false)]
    private ClientTimestamps $timestamps;

    public function __construct()
    {
        $this->metaData = new ClientMetaData();
        $this->timestamps = new ClientTimestamps();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getCreditPackage(): ?CreditPackage
    {
        return $this->creditPackage;
    }

    public function setCreditPackage(?CreditPackage $creditPackage): static
    {
        $this->creditPackage = $creditPackage;

        return $this;
    }

    public function loadMetaData(): ClientMetaData
    {
        return $this->metaData;
    }

    public function loadTimestamps(): ClientTimestamps
    {
        return $this->timestamps;
    }

}
