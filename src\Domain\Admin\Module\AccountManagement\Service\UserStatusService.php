<?php

/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/5/2025
 * @time 7:23 PM
 */
declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Service;

use App\Domain\Admin\Module\AccountManagement\Contract\UserStatusServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\UserStatus\UserStatusRequestDTO;
use App\Entity\Constant\UserConstant;
use App\Entity\User;
use App\Exception\LogicalException;
use App\Repository\UserRepository;
use DateTimeImmutable;

class UserStatusService implements UserStatusServiceInterface
{

    private UserRepository $userRepository;

    public function __construct(
        UserRepository $userRepository
    ) {
        $this->userRepository = $userRepository;
    }

    /**
     * @inheritDoc
     */
    public function activateUser(UserStatusRequestDTO $userStatusRequestDTO): bool
    {
        $user = $this->userRepository->find($userStatusRequestDTO->userId);

        if (!$user) {
            throw new LogicalException('User not found.');
        }

        if ($user->loadMetaData()->getStatus() === UserConstant::STATUS_ACTIVE) {
            return true;
        }

        $parentId = $user->loadMetaData()->getParentId();

        if ($parentId !== null) {
            $parentUser = $this->userRepository->find($parentId);

            if ($parentUser && $parentUser->loadMetaData()->getStatus() === UserConstant::STATUS_INACTIVE) {
                throw new LogicalException('The parent account is inactive. Cannot activate child.');
            }
        }

        $relatedEntities = [];

        $user->loadMetaData()->setStatus(UserConstant::STATUS_ACTIVE);

        if ($user->loadMetaData()->getStatus() === UserConstant::STATUS_BLOCKED) {
            $user->loadTimestamps()->setBlockedAt(null);
        }

        if ($user->loadMetaData()->getStatus() === UserConstant::STATUS_DELETED) {
            $user->loadTimestamps()->setDeletedAt(null);
        }

        $children = $this->userRepository->findWhere(['metaData.parentId' => $user->getId()]);

        foreach ($children as $child) {
            $child->loadMetaData()->setStatus(UserConstant::STATUS_ACTIVE);
            $child->loadTimestamps()->setBlockedAt(null);
            $child->loadTimestamps()->setDeletedAt(null);
            $relatedEntities[] = $child;
        }

        $result = $this->userRepository->save($user, $relatedEntities);
        if (!$result) {
            throw new LogicalException('Failed to activate user. Please try again.');
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function deactivateUser(UserStatusRequestDTO $userStatusRequestDTO): bool
    {
        $user = $this->userRepository->find($userStatusRequestDTO->userId);

        if (!$user) {
            throw new LogicalException('User not found.');
        }

        if (in_array(UserConstant::ROLE_ADMIN, $user->getRoles(), true)) {
            throw new LogicalException('Cannot deactivate an administrator account.');
        }

        if ($user->loadMetaData()->getStatus() === UserConstant::STATUS_INACTIVE) {
            return true;
        }

        $relatedEntities = [];
        $user->loadMetaData()->setStatus(UserConstant::STATUS_INACTIVE);

        $children = $this->userRepository->findWhere(['metaData.parentId' => $user->getId()]);

        foreach ($children as $child) {
            $child->loadMetaData()->setStatus(UserConstant::STATUS_INACTIVE);
            $relatedEntities[] = $child;
        }

        $result = $this->userRepository->save($user, $relatedEntities);
        if (!$result) {
            throw new LogicalException('Failed to deactivate user. Please try again.');
        }

        return true;
    }

    /**
     * @inheritDoc
     * @throws \DateMalformedStringException
     */
    public function restoreUser(UserStatusRequestDTO $userStatusRequestDTO): bool
    {
        $this->userRepository->getManager()->getFilters()->disable('softdeleteable');

        $user = $this->userRepository->find($userStatusRequestDTO->userId);

        if (!$user) {
            throw new LogicalException('User not found.');
        }

        if ($user->loadMetaData()->getStatus() !== UserConstant::STATUS_DELETED) {
            throw new LogicalException('This user is not deleted.');
        }

        $deletedAt = $user->loadTimestamps()->getDeletedAt();
        if (!$deletedAt || new DateTimeImmutable()->diff($deletedAt)->days > 30) {
            throw new LogicalException('Cannot restore: account was deleted over 30 days ago.');
        }

        $parentId = $user->loadMetaData()->getParentId();
        if ($parentId !== null) {
            $parentUser = $this->userRepository->find($parentId);

            if ($parentUser && $parentUser->loadMetaData()->getStatus() === UserConstant::STATUS_DELETED) {
                throw new LogicalException('Cannot restore child because parent is deleted.');
            }
        }

        $statusToRestore = $this->resolveRestoreStatus($user);
        $user->loadMetaData()->setStatus($statusToRestore);
        $user->loadTimestamps()->setDeletedAt(null);

        $relatedEntities = [];
        $threshold = new DateTimeImmutable()->modify('-30 days');
        $descendants = $this->userRepository->findRestorableDescendants($user->getId(), $threshold);

        foreach ($descendants as $descendant) {
            $descendant->loadMetaData()->setStatus($this->resolveRestoreStatus($descendant));
            $descendant->loadTimestamps()->setDeletedAt(null);
            $relatedEntities[] = $descendant;
        }

        $result = $this->userRepository->save($user, $relatedEntities);
        if (!$result) {
            throw new LogicalException('Failed to restore user. Please try again.');
        }

        return true;
    }

    /**
     * Determines restore status based on parent's status.
     */
    private function resolveRestoreStatus(User $user): int
    {
        $parentId = $user->loadMetaData()->getParentId();

        if ($parentId !== null) {
            $parent = $this->userRepository->find($parentId);

            if ($parent && $parent->loadMetaData()->getStatus() === UserConstant::STATUS_INACTIVE) {
                return UserConstant::STATUS_INACTIVE;
            }
        }
        return UserConstant::STATUS_ACTIVE;
    }

}
