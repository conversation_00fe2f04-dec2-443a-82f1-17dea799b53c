<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    14:25
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Domain\Admin\Module\AccountManagement\Contract\GetUserAccountDetailServiceInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AccountDetailController.
 *
 * This is the default controller of this module, handles the account-detail and related functionalities.
 */
#[Route('/admin/manage/account', name: 'app_admin_account_')]
class AccountDetailController extends AdminController
{

    private GetUserAccountDetailServiceInterface $getUserAccountDetailService;

    public function __construct(
        GetUserAccountDetailServiceInterface $getUserAccountDetailService
    ) {
        parent::__construct();
        $this->getUserAccountDetailService = $getUserAccountDetailService;
    }

    /**
     * Index action to display user account details.
     *
     * @param  int|string  $id  The unique identifier of the user account.
     *
     * @return Response Returns a Response object containing the rendered view of the account details.
     */
    #[Route('/detail/{id}', name: 'detail', methods: ['GET'])]
    public function index(int|string $id): Response
    {
        if (!is_numeric($id)) {
            flash()->error('Invalid user ID.');

            return $this->redirectToRoute('app_admin_dashboard_index');
        }

        $userAccountDetailDTO = $this->getUserAccountDetailService->getSummary((int)$id);

        if ($userAccountDetailDTO === null) {
            flash()->error('User not found');

            return $this->redirectToRoute('app_admin_dashboard_index');
        }

        return $this->view('/account-management/pages/detail-account/detail', [
            'accountInfo'          => $userAccountDetailDTO->accountInformationDTO ?? null,
            'userProfile'          => $userAccountDetailDTO->userProfileDTO ?? null,
            'userTypeSpecificData' => $userAccountDetailDTO->userTypeSpecificData ?? null,
            'wallet'               => $userAccountDetailDTO->userWalletDTO ?? null,
            'transactions'         => $userAccountDetailDTO->userWalletTransactionsDTO !== null ? $this->getTransactions(
                $userAccountDetailDTO->userWalletTransactionsDTO
            ) : null,
            'lastItemPurchased'    => $userAccountDetailDTO->lastItemPurchasedDTO ?? null,
            'id'                   => $id,
        ]);
    }

    /**
     * Get transactions.
     *
     * @param  array  $transactions  The array of transactions to process.
     *
     * @return array Returns an array of transactions, converting objects to arrays.
     */
    private function getTransactions(array $transactions): array
    {
        $result = [];

        foreach ($transactions as $transaction) {
            $result[] = $transaction !== null ? get_object_vars($transaction) : null;
        }

        return $result;
    }

}
