<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    01:05
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\DTO;

/**
 * Class AdminAccountPasswordResponseDTO.
 *
 * This class represents a Data Transfer Object (DTO) for handling the response of an admin account password change.
 * It contains a property for the new password.
 */
final class AdminAccountPasswordResponseDTO
{

    public ?string $password = null;

}
