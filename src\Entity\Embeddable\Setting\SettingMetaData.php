<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Setting;

use App\Entity\Trait\HasStatusTrait;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class SettingMetaData.
 *
 * This class represents meta-data about a setting.
 *
 * It is used as an embeddable entity in the Setting entity.
 */
#[ORM\Embeddable]
final class SettingMetaData
{

    use HasStatusTrait;

    #[ORM\Column(name: 'data_key', length: 64, unique: true)]
    private readonly string $dataKey;

    #[ORM\Column(name: 'data_value', type: 'json')]
    private readonly array $dataValue;

    #[ORM\Column(name: 'entity_id', type: 'bigint')]
    private int $entityId;

    #[ORM\Column(name: 'entity_class', length: 255)]
    private string $entityClass;

    public function __construct(string $dataKey, array $dataValue)
    {
        $this->dataKey = $dataKey;
        $this->dataValue = $dataValue;
    }

    public function getDataKey(): string
    {
        return $this->dataKey;
    }

    public function getDataValue(): array
    {
        return $this->dataValue;
    }

    public function getEntityId(): int
    {
        return $this->entityId;
    }

    public function setEntityId(int $entityId): SettingMetaData
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): SettingMetaData
    {
        $this->entityClass = $entityClass;

        return $this;
    }

}
