<?php

declare(strict_types=1);

namespace App\Base;

use App\Exception\InvalidCallbackException;
use App\Exception\InvalidEntityException;
use App\Exception\InvalidOperatorException;
use App\Exception\LogicalException;
use App\Exception\PersistenceException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Andx;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;

/**
 * Abstract class BaseRepository.
 *
 * Extends ServiceEntityRepository to provide common functionality for all repository classes.
 *
 * This class serves as the foundation for specific entity repositories in the application.
 */
abstract class BaseRepository extends ServiceEntityRepository
{

    protected string $entityManager;

    private ?string $errors = null;

    private EntityManagerInterface|ObjectManager $manager;

    private bool $enableCache = false;

    private int $cacheLifetime;

    public function __construct(ManagerRegistry $registry)
    {
        $entity = $this->loadEntity();

        if (empty($entity) || !class_exists($entity)) {
            throw new InvalidEntityException('Entity class must be valid and set in repository');
        }

        parent::__construct($registry, $entity);

        $this->manager = isset($this->entityManager) ? $registry->getManager($this->entityManager)
            : $this->getEntityManager();
    }

    /**
     * Load entity for the repository.
     *
     * This method should be implemented in the child class to return the entity class name.
     *
     * Expected to return a full qualified class name of the entity.
     *
     * @return string
     */
    abstract protected function loadEntity(): string;

    /**
     * Get the entity manager for the repository.
     *
     * This method returns the entity manager used by the repository.
     *
     * @return EntityManagerInterface|ObjectManager
     */
    public function getManager(): EntityManagerInterface|ObjectManager
    {
        return $this->manager;
    }

    /**
     * Enable query caching for the repository.
     *
     * This method enables caching for the repository with a specified cache lifetime.
     *
     * @param  int  $cacheLifeTime  The lifetime of the cache in seconds (default is 3600 seconds)
     */
    public function enableCache(int $cacheLifeTime = 3600): void
    {
        $this->enableCache = true;
        $this->cacheLifetime = $cacheLifeTime;
    }

    /**
     * Find entities based on conditions, with optional ordering, limit, and offset.
     *
     * This method allows you to find entities that match the specified conditions.
     * You can also specify ordering, limit, and offset for pagination.
     *
     * @param  array       $conditions  The conditions to filter entities by
     * @param  array|null  $orderBy     Optional associative array of field names and directions
     * @param  int|null    $limit       Optional limit for the number of results
     * @param  int|null    $offset      Optional offset for pagination
     *
     * @return array
     */
    public function findWhere(
        array $conditions,
        ?array $orderBy = null,
        ?int $limit = null,
        ?int $offset = null
    ): array {
        $qb = $this->createQueryBuilder('e');

        $this->applyConditions($qb, $conditions);

        if ($orderBy) {
            foreach ($orderBy as $field => $direction) {
                $qb->addOrderBy("e.$field", $direction);
            }
        }

        if ($limit !== null) {
            $qb->setMaxResults($limit);
        }

        if ($offset !== null) {
            $qb->setFirstResult($offset);
        }

        $query = $qb->getQuery();

        if ($this->enableCache) {
            $query->enableResultCache($this->cacheLifetime);
        }

        return $query->getResult();
    }

    /**
     * Save an entity and optional related entities.
     *
     * @param  BaseEntity                                   $entity           The entity to save
     * @param  BaseEntity[]                                 $relatedEntities  Optional related entities to persist
     * @param  callable(BaseEntity, BaseEntity): void|null  $callback         Optional callback to
     *                                                                        process related entities
     *
     * @return bool
     */
    public function save(BaseEntity $entity, array $relatedEntities = [], ?callable $callback = null): bool
    {
        try {
            if (!$entity->getId()) {
                $this->manager->persist($entity);
            }

            if (!empty($relatedEntities) && $callback !== null) {
                if (!is_callable($callback)) {
                    throw new InvalidCallbackException('Callback is not callable');
                }

                foreach ($relatedEntities as $relatedEntity) {
                    if (!$relatedEntity instanceof BaseEntity) {
                        throw new InvalidEntityException(
                            sprintf('Related entity must be instance of %s', BaseEntity::class)
                        );
                    }

                    try {
                        $callback($entity, $relatedEntity);
                    } catch (LogicalException $e) {
                        $this->errors = $e->getMessage();

                        return false;
                    }

                    $this->manager->persist($relatedEntity);
                }
            }

            $this->manager->flush();

            return true;
        } catch (PersistenceException $e) {
            $this->errors = $e->getMessage();

            return false;
        }
    }

    /**
     * Delete an entity and related optional related entities.
     *
     * @param  BaseEntity  $entity  The entity to delete
     *
     * @return bool
     */
    public function delete(BaseEntity $entity): bool
    {
        try {
            $this->manager->remove($entity);
            $this->manager->flush();

            return true;
        } catch (LogicalException $e) {
            $this->errors = $e->getMessage();

            return false;
        }
    }

    /**
     * Get the last error message.
     *
     * @return string|null
     */
    public function getErrors(): ?string
    {
        return $this->errors;
    }

    /**
     * Apply conditions to the query builder.
     *
     * This method applies the provided conditions to the query builder using AND logic.
     *
     * @param  QueryBuilder  $qb          The query builder instance
     * @param  array         $conditions  The conditions to apply
     */
    private function applyConditions(QueryBuilder $qb, array $conditions): void
    {
        $expr = $qb->expr();

        $expressions = [];

        foreach ($conditions as $key => $value) {
            if (is_array($value) && strtoupper($key) === 'OR') {
                $orGroup = $expr->orX();

                foreach ($value as $sub) {
                    $subExpr = $expr->andX();
                    $this->buildExpressionGroup($qb, $subExpr, $sub);

                    $orGroup->add($subExpr);
                }

                if (count($orGroup->getParts()) > 0) {
                    $expressions[] = $orGroup;
                }
            } else {
                $singleExpr = $expr->andX();
                $this->buildExpressionGroup($qb, $singleExpr, [$key => $value]);

                $expressions[] = $singleExpr;
            }
        }

        if (!empty($expressions)) {
            $finalExpr = $expr->andX(...$expressions);
            $qb->andWhere($finalExpr);
        }
    }

    /**
     * Build expression group for query conditions.
     *
     * This method constructs the expression group based on the provided conditions.
     *
     * @param  QueryBuilder  $qb          The query builder instance
     * @param  Andx          $exprGroup   The expression group to build
     * @param  array         $conditions  The conditions to apply
     */
    private function buildExpressionGroup(QueryBuilder $qb, Andx $exprGroup, array $conditions): void
    {
        static $paramIndex = 0;

        $expr = $qb->expr();

        foreach ($conditions as $field => $value) {
            $paramName = "param_".$paramIndex++;

            if (!is_array($value)) {
                $exprGroup->add($expr->eq("e.$field", ":$paramName"));
                $qb->setParameter($paramName, $value);
            } else {
                $operator = strtoupper($value[0]);

                switch ($operator) {
                    case '=':
                        $exprGroup->add($expr->eq("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case '!=':
                    case '<>':
                        $exprGroup->add($expr->neq("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case '>':
                        $exprGroup->add($expr->gt("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case '>=':
                        $exprGroup->add($expr->gte("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case '<':
                        $exprGroup->add($expr->lt("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case '<=':
                        $exprGroup->add($expr->lte("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case 'LIKE':
                        $exprGroup->add($expr->like("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case 'NOT LIKE':
                        $exprGroup->add($expr->notLike("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case 'IN':
                        $exprGroup->add($expr->in("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case 'NOT IN':
                        $exprGroup->add($expr->notIn("e.$field", ":$paramName"));
                        $qb->setParameter($paramName, $value[1]);
                        break;
                    case 'IS NULL':
                        $exprGroup->add($expr->isNull("e.$field"));
                        break;
                    case 'IS NOT NULL':
                        $exprGroup->add($expr->isNotNull("e.$field"));
                        break;
                    case 'BETWEEN':
                        $paramStart = $paramName.'_start';
                        $paramEnd = $paramName.'_end';
                        $exprGroup->add($expr->between("e.$field", ":$paramStart", ":$paramEnd"));
                        $qb->setParameter($paramStart, $value[1]);
                        $qb->setParameter($paramEnd, $value[2]);
                        break;
                    default:
                        throw new InvalidOperatorException("Unsupported operator: $operator");
                }
            }
        }
    }

}
