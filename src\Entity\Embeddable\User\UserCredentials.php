<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\User;

use App\Entity\Constant\UserConstant;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserCredentials.
 *
 * This class represents the credentials of a user, including email, password, and roles.
 *
 * It is used as an embeddable entity in the User entity.
 */
#[ORM\Embeddable]
final class UserCredentials
{

    #[ORM\Column(name: 'email', unique: true)]
    private readonly string $email;

    #[ORM\Column(name: 'password')]
    private string $password;

    #[ORM\Column(name: 'roles', type: 'json')]
    private array $roles = [UserConstant::ROLE_USER];

    public function __construct(string $email)
    {
        $this->email = $email;
    }

    /**
     * Check if the user has a specific role(s) or not.
     *
     * @param  string|array<string>  $roles  Single role or array of roles to check
     * @param  bool                  $requireAll  Whether all roles must be present
     *
     * @return bool
     */
    public function hasRoles(string|array $roles, bool $requireAll = false): bool
    {
        $roles = is_array($roles) ? $roles : [$roles];
        $intersect = array_intersect($this->roles, $roles);

        return $requireAll ? count($intersect) === count($roles) : !empty($intersect);
    }

    /**
     * Assign additional roles to the user.
     *
     * @param  string|array<string>  $roles  Single role or array of roles to assign
     *
     * @return static
     */
    public function assignNewRole(string|array $roles): UserCredentials
    {
        $roles = is_array($roles) ? $roles : [$roles];
        $validRoles = array_filter(
            $roles,
            fn(string $role) => array_key_exists($role, UserConstant::ROLES)
                && !in_array($role, $this->roles, true)
        );

        if ($validRoles !== []) {
            $this->setRoles(array_merge($this->roles, $validRoles));
        }

        return $this;
    }

    /**
     * Revoke specific roles from the user. If all roles was removed, assigns the default user role.
     *
     * @param  string|array<string>  $roles  Single role or array of roles to revoke
     *
     * @return static
     */
    public function revokeRole(string|array $roles): UserCredentials
    {
        $roles = is_array($roles) ? $roles : [$roles];
        $rolesToRemove = array_filter(
            $roles,
            fn(string $role) => array_key_exists($role, UserConstant::ROLES)
                && in_array($role, $this->roles, true)
        );

        if ($rolesToRemove !== []) {
            $newRoles = array_values(array_diff($this->roles, $rolesToRemove));

            $this->setRoles($newRoles ?: [UserConstant::ROLE_USER]);
        }

        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): UserCredentials
    {
        $this->password = $password;

        return $this;
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function setRoles(array $roles = []): UserCredentials
    {
        $defaults = array_keys(UserConstant::ROLES);
        $validRoles = array_filter($roles, static fn(string $role) => in_array($role, $defaults, true));

        $this->roles = array_unique($validRoles);

        return $this;
    }

}
