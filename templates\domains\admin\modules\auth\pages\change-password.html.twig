{% extends 'domains/admin/shared/layouts/auth.html.twig' %}

{% block title %}
    Change Password
{% endblock %}

{% block form %}
    <div id="adminChangePasswordView">
        <form action="{{ path('app_admin_auth_change_password') }}" method="post">
            <label class="mt-4 block">
                <span>Current Password:</span>
                <span class="relative mt-1.5 flex">
                    <input
                            class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:z-10 hover:border-slate-400 focus:z-10 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="Enter Password"
                            type="password"
                            name="current_password"
                            id="current_password"
                            required
                    />
                    <span
                            class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                    >
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                            ></path>
                        </svg>
                    </span>
                    <button
                            class="cursor-pointer absolute right-0 flex h-full w-10 items-center justify-center text-slate-400 hover:text-primary dark:text-navy-300 dark:hover:text-accent z-10"
                            onclick="togglePasswordVisibility('current_password')"
                            type="button"
                    >
                        <!-- Eye icon -->
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 toggle-password-icon"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            ></path>
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            ></path>
                        </svg>
                    </button>
                </span>
            </label>
            <label class="mt-4 block">
                <span>New Password:</span>
                <span class="relative mt-1.5 flex">
                    <input
                            class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:z-10 hover:border-slate-400 focus:z-10 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="Enter Password"
                            type="password"
                            name="new_password"
                            id="new_password"
                            required
                    />
                    <span
                            class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                    >
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                            ></path>
                        </svg>
                    </span>
                    <button
                            class="cursor-pointer absolute right-0 flex h-full w-10 items-center justify-center text-slate-400 hover:text-primary dark:text-navy-300 dark:hover:text-accent z-10"
                            onclick="togglePasswordVisibility('new_password')"
                            type="button"
                    >
                        <!-- Eye icon -->
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 toggle-password-icon"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            ></path>
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            ></path>
                        </svg>
                    </button>
                </span>
            </label>
            <label class="mt-4 block">
                <span>Confirm New Password:</span>
                <span class="relative mt-1.5 flex">
                    <input
                            class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:z-10 hover:border-slate-400 focus:z-10 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                            placeholder="Enter Password"
                            type="password"
                            name="confirm_new_password"
                            id="confirm_new_password"
                            required
                    />
                    <span
                            class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                    >
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                            ></path>
                        </svg>
                    </span>
                    <button
                            class="cursor-pointer absolute right-0 flex h-full w-10 items-center justify-center text-slate-400 hover:text-primary dark:text-navy-300 dark:hover:text-accent z-10"
                            onclick="togglePasswordVisibility('confirm_new_password')"
                            type="button"
                    >
                        <!-- Eye icon -->
                        <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 toggle-password-icon"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                        >
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            ></path>
                            <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            ></path>
                        </svg>
                    </button>
                </span>
            </label>
            <div
                    class="mt-4 flex items-center justify-between space-x-2"
            >
                <div></div>
                <a
                        href="{{ path('app_admin_dashboard_index')|default('javascript:void(0)') }}"
                        class="text-xs text-slate-400 transition-colors line-clamp-1 hover:text-slate-800 focus:text-slate-800 dark:text-navy-300 dark:hover:text-navy-100 dark:focus:text-navy-100"
                >Back to Dashboard</a
                >
            </div>
            <button
                    class="btn mt-5 w-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90"
                    type="submit"
            >
                Save Change
            </button>
        </form>
    </div>

    <script>
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.parentNode.querySelector('.toggle-password-icon');
            if (input.type === 'password') {
                input.type = 'text';
                icon.innerHTML = `
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                    />
                `;
            } else {
                input.type = 'password';
                icon.innerHTML = `
                   <path
                       stroke-linecap="round"
                       stroke-linejoin="round"
                       stroke-width="1.5"
                       d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                   />
                   <path
                       stroke-linecap="round"
                       stroke-linejoin="round"
                       stroke-width="1.5"
                       d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                   />
                `;
            }
        }
    </script>

{% endblock %}
