{% extends 'domains/admin/modules/account-management/pages/list-account/list.html.twig' %}

{% block data_table %}
    <div class="container mx-auto py-4" style="overflow: visible;">
        <!-- Header Section -->
        <div class="flex items-center justify-between my-3">
            <h2 class=" text-base font-medium tracking-wide text-slate-700 line-clamp-1 dark:text-navy-100
            ">
                All Accounts
            </h2>
            <div class="flex items-center space-x-2">

                <!-- Filter Button -->
                <button
                    @click="isFilterExpanded = !isFilterExpanded"
                    class="btn h-9 font-medium hover:bg-primary-focus hover:text-white hover:border-primary-focus inline-flex items-center gap-2 focus:outline-none"
                    :class="isFilterExpanded ? 'bg-primary text-white border-primary' : 'bg-white text-gray-700 border border-gray-200'"
                
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                    </svg>
                    Filter
                </button>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section" x-show="isFilterExpanded" x-collapse>
            <div class="filter-header">
                <span>Filter Accounts</span>
            </div>
            <div class="filter-content">
                <div class="max-w-6xl">
                    <!-- Text Filters -->
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 sm:gap-5 lg:gap-6 mb-6">
                        <!-- Parent Filter -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Parent:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Enter Parent"
                                    type="text"
                                    id="filter-parent"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg xmlns="http://www.w3.org/2000/svg" class="size-4.5 transition-colors duration-200"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v13.5"/>
                            </svg>
                        </span>
                            </div>
                        </label>

                        <!-- Username Filter -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Username:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Enter Username"
                                    type="text"
                                    id="filter-username"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-4.5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                    d="M5 19.111c0-2.413 1.697-4.468 4.004-4.848l.208-.035a17.134 17.134 0 015.576 0l.208.035c2.307.38 4.004 2.435 4.004 4.848C19 20.154 18.181 21 17.172 21H6.828C5.818 21 5 20.154 5 19.111zM16.083 6.938c0 2.174-1.828 3.937-4.083 3.937S7.917 9.112 7.917 6.937C7.917 4.764 9.745 3 12 3s4.083 1.763 4.083 3.938z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>

                        <!-- Email Filter -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Email:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Enter Email"
                                    type="text"
                                    id="filter-email"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-4.5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>
                    </div>

                    <!-- Dropdown Filters -->
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-2 sm:gap-5 lg:gap-6 mb-6 py-2">
                        <!-- Role Filter -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Role:</span>
                            <div class="relative mt-1.5">
                                <select
                                    class="form-select w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent"
                                    id="filter-role"
                                >
                                    <option value="">All Roles</option>
                                    <option value="ROLE_ADMIN">Admin</option>
                                    <option value="ROLE_RESELLER">Reseller</option>
                                    <option value="ROLE_CLIENT">Client</option>
                                    <option value="ROLE_PARTNER">Partner</option>
                                    <option value="ROLE_USER">User</option>
                                </select>
                            </div>
                        </label>

                        <!-- Status Filter -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Status:</span>
                            <div class="relative mt-1.5">
                                <select
                                    class="form-select w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent"
                                    id="filter-status"
                                >
                                    <option value="">All Status</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                    <option value="2">Blocked</option>
                                    <option value="3">Deleted</option>
                                </select>
                            </div>
                        </label>
                    </div>

                    <!-- Date Range Filters -->
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-2 sm:gap-5 lg:gap-6 mb-6">
                        <!-- Last Login At -->
                        <label class="block">
                                <span
                                    class="text-sm font-medium text-slate-700 dark:text-navy-100">Last Login At:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    x-init="$el._x_flatpickr = flatpickr($el, {mode: 'range', dateFormat: 'Y-m-d', allowInput: true})"
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Choose date range..."
                                    type="text"
                                    id="filter-last-login-range"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                stroke-width="1.5"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>

                        <!-- Blocked At -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Blocked At:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    x-init="$el._x_flatpickr = flatpickr($el, {mode: 'range', dateFormat: 'Y-m-d', allowInput: true})"
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Choose date range..."
                                    type="text"
                                    id="filter-blocked-at-range"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                stroke-width="1.5"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>

                        <!-- Created At -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Created At:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    x-init="$el._x_flatpickr = flatpickr($el, {mode: 'range', dateFormat: 'Y-m-d', allowInput: true})"
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Choose date range..."
                                    type="text"
                                    id="filter-created-at-range"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                stroke-width="1.5"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>

                        <!-- Updated At -->
                        <label class="block">
                            <span class="text-sm font-medium text-slate-700 dark:text-navy-100">Updated At:</span>
                            <div class="relative mt-1.5 flex">
                                <input
                                    x-init="$el._x_flatpickr = flatpickr($el, {mode: 'range', dateFormat: 'Y-m-d', allowInput: true})"
                                    class="form-input peer w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 pl-9 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent"
                                    placeholder="Choose date range..."
                                    type="text"
                                    id="filter-updated-at-range"
                                />
                                <span
                                    class="pointer-events-none absolute flex h-full w-10 items-center justify-center text-slate-400 peer-focus:text-primary dark:text-navy-300 dark:peer-focus:text-accent"
                                >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="size-5 transition-colors duration-200"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                stroke-width="1.5"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                        </span>
                            </div>
                        </label>
                    </div>

                    <!-- Filter Actions -->
                    <div class="mt-6 flex items-center justify-end space-x-2">
                        <button
                            onclick="clearFilters()"
                            class="btn font-medium text-slate-700 hover:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:active:bg-navy-300/25"
                        >
                            Clear
                        </button>
                        <button
                            @click="isFilterExpanded = false"
                            class="btn font-medium text-slate-700 hover:bg-slate-300/20 active:bg-slate-300/25 dark:text-navy-100 dark:hover:bg-navy-300/20 dark:active:bg-navy-300/25"
                        >
                            Cancel
                        </button>
                        <button
                            onclick="applyFilters()"
                            @click="isFilterExpanded = false"
                            class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90"
                        >
                            Apply Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions Bar -->
        <div id="bulk-actions" class="bulk-actions" style="display: none;" x-data="{ isMobileDropdownOpen: false }">
            <div class="flex items-center justify-between">
                <!-- Selected Count -->
                <div class="flex items-center space-x-2 text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span id="selected-count">0</span>
                    <span class="hidden sm:inline">items selected</span>
                    <span class="sm:hidden">selected</span>
                </div>

                <!-- Desktop Actions (hidden on mobile) -->
                <div class="hidden lg:flex items-center space-x-2">
                    <button onclick="bulkActivate()" class="bulk-actions-button">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Activate
                    </button>
                    <button onclick="bulkDeactivate()" class="bulk-actions-button">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Deactivate
                    </button>
                    <button onclick="bulkUnblock()" class="bulk-actions-button">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Unblock
                    </button>
                    <button onclick="bulkBlock()" class="bulk-actions-button">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Block
                    </button>
                    <button onclick="clearSelection()" class="bulk-actions-button danger">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Clear
                    </button>
                </div>

                <!-- Mobile Actions Dropdown (visible on mobile/tablet) -->
                <div class="lg:hidden flex items-center space-x-2">
                    <button onclick="clearSelection()" class="bulk-actions-button" style="min-height: 39px">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>

                    <div class="relative" @click.outside="isMobileDropdownOpen = false" style="min-height: 39px">
                        <button @click="isMobileDropdownOpen = !isMobileDropdownOpen"
                                x-ref="actionButton"
                                class="bulk-actions-button flex items-center">
                            <span class="mr-1">Actions</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform"
                                 :class="isMobileDropdownOpen ? 'rotate-180' : ''"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        <!-- Mobile Dropdown Menu -->
                        <div x-show="isMobileDropdownOpen"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-1 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-1 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 dark:bg-navy-700 dark:border-navy-500 mobile-dropdown-menu"
                             style="z-index: 99999;">

                            <button onclick="bulkActivate()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-navy-100 dark:hover:bg-navy-600 mobile-dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-green-500"
                                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Activate Users
                            </button>

                            <button onclick="bulkDeactivate()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-navy-100 dark:hover:bg-navy-600 mobile-dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-yellow-500"
                                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Deactivate Users
                            </button>

                            <button onclick="bulkBlock()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-navy-100 dark:hover:bg-navy-600 mobile-dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-orange-500"
                                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"/>
                                </svg>
                                Block Users
                            </button>

                            <button onclick="bulkUnblock()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-navy-100 dark:hover:bg-navy-600 mobile-dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-blue-500"
                                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"/>
                                </svg>
                                Unblock Users
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Card -->
        <div class="card" style="overflow: visible;">
            {% block account_list_tabulator_init %}{% endblock %}

            <!-- Custom Pagination -->
            <div
                class="flex flex-col justify-between space-y-4 px-4 py-3 sm:flex-row sm:items-center sm:space-y-0 sm:px-5">
                <div class="flex items-center space-x-2 text-xs">
                    <span>Show</span>
                    <label class="block">
                        <select id="page-size"
                                class="form-select rounded-full border border-slate-300 bg-white px-2 py-1 pr-6 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </label>
                    <span>entries</span>
                </div>

                <div id="pagination" class="flex items-center">
                    <!-- Pagination will be generated here -->
                </div>

                <div id="table-info" class="text-xs">
                    <!-- Table info will be updated here -->
                </div>
            </div>
        </div>
    </div>
{% endblock %}
