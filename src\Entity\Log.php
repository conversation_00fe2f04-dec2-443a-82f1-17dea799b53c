<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\Log\LogMetaData;
use App\Entity\Embeddable\Log\LogTimestamps;
use App\Repository\LogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Log.
 *
 * Represents a log entry in the system. Maps to the 'logs' table in the database.
 */
#[ORM\Entity(repositoryClass: LogRepository::class)]
#[ORM\Table(name: 'logs')]
#[ORM\Index(name: 'log_entry_level_idx', columns: ['level'])]
#[ORM\Index(name: 'log_entry_channel_idx', columns: ['channel'])]
#[ORM\Index(name: 'log_entry_logged_at_idx', columns: ['logged_at'])]
class Log extends BaseEntity
{

    #[ORM\Column(type: Types::STRING, length: 255)]
    private ?string $channel = null;

    #[ORM\Embedded(class: LogMetaData::class, columnPrefix: false)]
    private LogMetaData $metaData;

    #[ORM\Embedded(class: LogTimestamps::class, columnPrefix: false)]
    private LogTimestamps $timestamps;

    public function __construct()
    {
        $this->metaData = new LogMetaData();
        $this->timestamps = new LogTimestamps();
    }

    public function getChannel(): ?string
    {
        return $this->channel;
    }

    public function setChannel(?string $channel): self
    {
        $this->channel = $channel;
        return $this;
    }

    public function loadMetaData(): LogMetaData
    {
        return $this->metaData;
    }

    public function loadLogTimestamps(): LogTimestamps
    {
        return $this->timestamps;
    }

}
