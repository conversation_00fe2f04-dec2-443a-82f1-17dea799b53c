security:
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    providers:
        admin_user_provider:
            entity:
                class: App\Entity\User
                property: credentials.email

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        admin_area:
            pattern: ^/admin
            provider: admin_user_provider
            context: admin_context
            user_checker: App\Domain\Admin\Module\Auth\Security\Checker\AdminUserChecker
            form_login:
                login_path: app_admin_auth_login
                check_path: app_admin_auth_login
                default_target_path: app_admin_dashboard_index
                failure_handler: App\Domain\Admin\Module\Auth\Security\Handler\AdminAuthenticationFailureHandler
                success_handler: App\Domain\Admin\Module\Auth\Security\Handler\AdminAuthenticationSuccessHandler
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800
            logout:
                path: app_admin_auth_logout
                target: app_admin_auth_login

    access_control:
        - { path: ^/admin/login$, roles: PUBLIC_ACCESS }
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/, roles: PUBLIC_ACCESS }

when@test:
    security:
        password_hashers:
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4
                time_cost: 3
                memory_cost: 10
