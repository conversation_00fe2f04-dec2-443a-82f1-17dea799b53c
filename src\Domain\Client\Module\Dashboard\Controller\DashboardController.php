<?php

declare(strict_types=1);

namespace App\Domain\Client\Module\Dashboard\Controller;

use App\Domain\Client\Base\ClientController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DashboardController.
 *
 * This is the default controller of this module, handles the client-dashboard and related functionalities.
 */
#[Route('/client', name: 'app_client_dashboard_')]
class DashboardController extends ClientController
{

    /**
     * Action `index`.
     *
     * This is the default action of this module, handles the admin-dashboard request.
     */
    #[Route(name: 'index', methods: ['GET'])]
    public function index(): Response
    {
        $welcomeMessage = 'Welcome to ZenShop!';

        return $this->view('dashboard/pages/index', ['welcome_message' => $welcomeMessage]);
    }

}
