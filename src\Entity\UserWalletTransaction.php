<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\UserWalletTransaction\UserWalletTransactionMetaData;
use App\Entity\Embeddable\UserWalletTransaction\UserWalletTransactionTimestamps;
use App\Repository\UserWalletTransactionRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserWalletTransaction.
 *
 * Represents a client in the system. Maps to the `user_wallet_transactions` table in the database.
 */
#[ORM\Entity(repositoryClass: UserWalletTransactionRepository::class)]
#[ORM\Table(name: 'user_wallet_transactions')]
class UserWalletTransaction extends BaseEntity
{

    #[ORM\ManyToOne(targetEntity: UserWallet::class, inversedBy: 'userWalletTransactions')]
    #[ORM\JoinColumn(name: 'user_wallet_id', referencedColumnName: 'id', onDelete: 'CASCADE')]
    private UserWallet $userWallet;

    #[ORM\Embedded(class: UserWalletTransactionMetaData::class, columnPrefix: false)]
    private UserWalletTransactionMetaData $metaData;

    #[ORM\Embedded(class: UserWalletTransactionTimestamps::class, columnPrefix: false)]
    private UserWalletTransactionTimestamps $timestamps;

    public function __construct()
    {
        $this->metaData = new UserWalletTransactionMetaData();
        $this->timestamps = new UserWalletTransactionTimestamps();
    }

    public function getUserWallet(): UserWallet
    {
        return $this->userWallet;
    }

    public function setUserWallet(UserWallet $userWallet): static
    {
        $this->userWallet = $userWallet;

        return $this;
    }

    public function loadMetaData(): UserWalletTransactionMetaData
    {
        return $this->metaData;
    }

    public function loadTimestamps(): UserWalletTransactionTimestamps
    {
        return $this->timestamps;
    }

}
