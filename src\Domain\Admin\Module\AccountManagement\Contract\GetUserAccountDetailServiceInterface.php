<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    15:58
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Contract;

use App\Domain\Admin\Module\AccountManagement\DTO\AccountInformationDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\LastItemPurchased\LastCreditPackagePurchasedDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserAccountDetailDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserProfileDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserTypeInfo\ClientDataDTO;
use App\Domain\Admin\Module\AccountManagement\DTO\UserWalletDTO;

/**
 * Interface for a service that handles the retrieval of user account details.
 *
 * This contract defines the methods that any service implementation for fetching
 * user account details must adhere to. It decouples the application's business
 * logic from the underlying data retrieval mechanism.
 */
interface GetUserAccountDetailServiceInterface
{
    /**
     * Retrieves the details of a user account by its unique identifier.
     *
     * @param int $id The identifier of the user account.
     *
     * @return UserAccountDetailDTO|null The user account detail DTO.
     */
    public function getSummary(int $id): ?UserAccountDetailDTO;

    /**
     * Retrieves the account information for a user by their unique identifier.
     *
     * @param int $id The identifier of the user account.
     *
     * @return AccountInformationDTO|null The account information DTO.
     */
    public function getAccountInformation(int $id): ?AccountInformationDTO;

    /**
     * Retrieves the user profile by the user's unique identifier.
     *
     * @param int $userId The unique identifier of the user.
     *
     * @return UserProfileDTO|null The user profile DTO, or null if not found.
     */
    public function getUserProfile(int $userId): ?UserProfileDTO;

    /**
     * Retrieves the user wallet by the user's unique identifier.
     *
     * @param int $userId The unique identifier of the user.
     *
     * @return UserWalletDTO|null The user wallet DTO, or null if not found.
     */
    public function getUserWallet(int $userId): ?UserWalletDTO;

    /**
     * Retrieves the user wallet transactions by the user's wallet identifier.
     *
     * @param int|null $userWalletId The identifier of the user's wallet.
     *
     * @return array|null An array of user wallet transactions, or null if not found.
     */
    public function getUserWalletTransactions(int|null $userWalletId): ?array;

    /**
     * Retrieves the client data for a user by their unique identifier.
     *
     * @param int $id The unique identifier of the user.
     *
     * @return ClientDataDTO|null The client data DTO, or null if not found.
     */
    public function getClientData(int $id): ?ClientDataDTO;

    /**
     * Retrieves the reseller data for a user by their unique identifier.
     *
     * @return null Returns null as this method is not implemented.
     */
    public function getResellerData(): null;

    /**
     * Retrieves the partner data for a user by their unique identifier.
     *
     * @return null Returns null as this method is not implemented.
     */
    public function getPartnerData(): null;

    /**
     * Retrieves the last plan purchased data for a user.
     *
     * @return null Returns null as this method is not implemented.
     */
    public function getLastPlanPurchasedData(): null;

    /**
     * Retrieves the last credit package purchased data for a user by their unique identifier.
     *
     * @param int|null $id The unique identifier of the user.
     *
     * @return LastCreditPackagePurchasedDTO|null The last credit package purchased DTO, or null if not found.
     */
    public function getLastCreditPackagePurchasedData(int|null $id): ?LastCreditPackagePurchasedDTO;

    /**
     * Retrieves the last program-purchased data for a user.
     *
     * @return null Returns null as this method is not implemented.
     */
    public function getLastProgramPurchasedData(): null;
}
