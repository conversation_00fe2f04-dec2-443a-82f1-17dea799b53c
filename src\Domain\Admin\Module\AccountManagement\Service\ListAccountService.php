<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email  <EMAIL>
 * @date    6/18/2025
 * @time    7:17 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Service;

use App\Domain\Admin\Module\AccountManagement\Contract\ListAccountServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\ListAccountDTO;
use App\Entity\Constant\UserConstant;
use App\Repository\UserRepository;

/**
 * Class ListAccountService.
 *
 * This service is responsible for managing user accounts within the application.
 * It allows for bulk operations (e.g., activate, deactivate, block, unblock),
 * as well as retrieving detailed account information and account status updates.
 *
 */
class ListAccountService implements ListAccountServiceInterface
{

    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * @inheritDoc
     */
    public function getList(): ?ListAccountDTO
    {
        $this->userRepository->getManager()->getFilters()->disable('softdeleteable');
        $userAccountList = $this->userRepository->findAll();
        $count = 1;

        $dto = new ListAccountDTO();
        foreach ($userAccountList as $userAccount) {
            if (in_array(UserConstant::ROLE_ADMIN, $userAccount->loadCredentials()->getRoles(), true)) {
                continue;
            }

            $user = [];
            $user['id'] = $userAccount->getId();
            $user['serial_number'] = $count;
            $user['username'] = $userAccount->loadMetaData()->getUsername();
            $user['email'] = $userAccount->loadCredentials()->getEmail();
            $user['roles'] = $userAccount->loadCredentials()->getRoles();
            $user['last_purchased'] = $this->getFormatedLastPurchased($userAccount);
            $user['registration_ip'] = $userAccount->loadMetaData()->getRegistrationIp() ?? "-";
            $user['status'] = $userAccount->loadMetaData()->getStatus();
            $user['last_login_at'] = $userAccount->loadTimestamps()->getLastLoginAt()?->format(DATE_ATOM) ?? "-";
            $user['blocked_at'] = $userAccount->loadTimestamps()->getBlockedAt()?->format(DATE_ATOM) ?? "-";
            $user['deleted_at'] = $userAccount->loadTimestamps()->getDeletedAt()?->format(DATE_ATOM) ?? "-";
            $user['created_at'] = $userAccount->getCreatedAt()->format(DATE_ATOM);
            $user['updated_at'] = $userAccount->getUpdatedAt()->format(DATE_ATOM);
            $parentId = $userAccount->loadMetaData()->getParentId();

            if ($parentId !== null) {
                $user['parent'] = $this->userRepository->find($parentId)?->loadMetaData()->getUsername();
            } else {
                $user['parent'] = "-";
            }

            $dto->accounts[] = $user;
            $count++;
        }

        return $dto;
    }

    /**
     * Retrieve the last purchased item for a user.
     *
     * Depending on the user's role, this method fetches different types of items purchased (e.g., package, plan).
     *
     * @param  mixed  $userAccount  The user account to retrieve purchase information for
     *
     * @return string The last item purchased by the user
     */
    private function getFormatedLastPurchased(mixed $userAccount): string
    {
        $result = "-";

        switch ($userAccount->loadCredentials()->getRoles()) {
            case UserConstant::ROLE_RESELLER:
                $packageName = "Coming soon";
                $result = "[Plan] ".$packageName;
                break;

            case UserConstant::ROLE_PARTNER:
                $packageName = "Coming soon 2";
                $result = "[Program] ".$packageName;
                break;

            case UserConstant::ROLE_CLIENT:
                $packageName = $userAccount->getClient()?->getCreditPackage()->loadMetaData()->getName();

                if ($packageName === null) {
                    break;
                }

                $result = "[Package] ".$packageName;
                break;

            default:
                break;
        }

        return $result;
    }

}
