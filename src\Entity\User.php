<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\User\UserCredentials;
use App\Entity\Embeddable\User\UserMetaData;
use App\Entity\Embeddable\User\UserTimestamps;
use App\Entity\Trait\HasCollectionTrait;
use App\Entity\Trait\HasLifeCycleTrait;
use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class User.
 *
 * Represents a user in the system. Maps to the `users` table in the database.
 */
#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: 'users')]
#[ORM\HasLifecycleCallbacks]
#[Gedmo\SoftDeleteable(fieldName: 'timestamps.deletedAt', timeAware: false, hardDelete: true)]
class User extends BaseEntity implements UserInterface, PasswordAuthenticatedUserInterface
{

    use HasLifeCycleTrait;
    use HasCollectionTrait;

    #[ORM\OneToOne(targetEntity: UserProfile::class, mappedBy: 'user', cascade: ['persist', 'remove'])]
    private ?UserProfile $userProfile = null;

    #[ORM\OneToMany(targetEntity: UserPaymentMethod::class, mappedBy: 'user', cascade: ['persist', 'remove'])]
    private Collection $userPaymentMethods;

    #[ORM\OneToMany(targetEntity: Setting::class, mappedBy: 'user', cascade: ['persist', 'remove'])]
    private Collection $settings;

    #[ORM\OneToOne(targetEntity: UserWallet::class, mappedBy: 'user', cascade: ['persist'])]
    private ?UserWallet $userWallet = null;

    #[ORM\OneToOne(targetEntity: Client::class, mappedBy: 'user', cascade: ['persist'])]
    private ?Client $client = null;

    #[ORM\Embedded(class: UserCredentials::class, columnPrefix: false)]
    private UserCredentials $credentials;

    #[ORM\Embedded(class: UserMetaData::class, columnPrefix: false)]
    private UserMetaData $metaData;

    #[ORM\Embedded(class: UserTimestamps::class, columnPrefix: false)]
    private UserTimestamps $timestamps;

    public function __construct(string $email)
    {
        $this->credentials = new UserCredentials($email);
        $this->metaData = new UserMetaData();
        $this->timestamps = new UserTimestamps();

        $this->userPaymentMethods = new ArrayCollection();
        $this->settings = new  ArrayCollection();
    }

    public function getUserPaymentMethods(): Collection
    {
        return $this->userPaymentMethods;
    }

    public function getUserProfile(): ?UserProfile
    {
        return $this->userProfile;
    }

    public function setUserProfile(?UserProfile $userProfile): static
    {
        if ($this->userProfile === null) {
            $this->userProfile = $userProfile;
        }

        return $this;
    }

    public function getSettings(): Collection
    {
        return $this->settings;
    }

    public function getUserWallet(): ?UserWallet
    {
        return $this->userWallet;
    }

    public function setUserWallet(?UserWallet $userWallet): static
    {
        if ($this->userWallet === null) {
            $this->userWallet = $userWallet;
        }

        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): static
    {
        if ($this->client === null) {
            $this->client = $client;
        }

        return $this;
    }

    public function loadCredentials(): UserCredentials
    {
        return $this->credentials;
    }

    public function loadMetaData(): UserMetaData
    {
        return $this->metaData;
    }

    public function loadTimestamps(): UserTimestamps
    {
        return $this->timestamps;
    }

    public function getPassword(): ?string
    {
        return $this->credentials->getPassword();
    }

    public function getRoles(): array
    {
        return $this->credentials->getRoles();
    }

    public function getUserIdentifier(): string
    {
        return $this->credentials->getEmail();
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here.
    }

}
