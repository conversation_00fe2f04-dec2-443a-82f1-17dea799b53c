<?php

declare(strict_types=1);

namespace App\Utils\Helper;

use DateInterval;
use DateInvalidTimeZoneException;
use DateMalformedStringException;
use DateTime;
use DateTimeImmutable;
use DateTimeZone;

/**
 * Class DateTimeHelper.
 *
 * A utility class that provides helper methods for working with DateTime objects.
 * This class includes methods for formatting, comparing, and manipulating DateTime instances.
 */
class DateTimeHelper
{

    /**
     * Get all the time zones with the offsets sorted by their offset
     *
     * @return array
     *
     * @throws DateInvalidTimeZoneException
     * @throws DateMalformedStringException
     */
    public static function timeZones(): array
    {
        $timeZones = [];
        $timeZoneIdentifiers = DateTimeZone::listIdentifiers();

        foreach ($timeZoneIdentifiers as $timeZone) {
            $date = new DateTime('now', new DateTimeZone($timeZone));
            $offset = $date->getOffset();
            $tz = ($offset > 0 ? '+' : '-').gmdate('H:i', abs($offset));
            $timeZones[] = [
                'timezone' => $timeZone,
                'name'     => "(UTC {$tz}) {$timeZone}",
                'offset'   => $offset,
            ];
        }

        array_multisort($timeZones, 'offset', SORT_DESC, SORT_NUMERIC);

        return $timeZones;
    }

    /**
     * Returns the current date and time as a DateTimeImmutable object.
     *
     * @param  DateTimeImmutable|null  $start  The start date and time. If null, the current date and time is used.
     * @param  DateTimeImmutable|null  $end    The end date and time. If null, the current date and time is used.
     *
     * @return DateInterval|false The current date and time.
     */
    public static function diff(?DateTimeImmutable $start = null, ?DateTimeImmutable $end = null): DateInterval|false
    {
        if (is_null($start)) {
            $start = new DateTimeImmutable();
        }

        if (is_null($end)) {
            $end = new DateTimeImmutable();
        }

        return $end->diff($start);
    }

    /**
     * Returns the time in a specified format from a DateTimeImmutable object.
     *
     * @param  DateTimeImmutable  $dateTime  The DateTimeImmutable object to format.
     * @param  string             $format    The format to return the date in. Defaults to 'Y-m-d H:i:s'.
     *
     * @return string The formatted date string.
     */
    public function getTime(DateTimeImmutable $dateTime, string $format = 'Y-m-d H:i:s'): string
    {
        return $dateTime->format($format);
    }

}
