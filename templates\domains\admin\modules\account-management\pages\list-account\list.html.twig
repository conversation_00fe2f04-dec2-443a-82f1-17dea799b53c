{% extends 'domains/admin/shared/layouts/main.html.twig' %}

{% block title %}Manage User Account{% endblock %}

{% block content %}
    <div class="col-span-12 bg-slate-50 dark:bg-navy-900" x-data="{ darkMode: false, isFilterExpanded: false }"
         :class="{ 'dark': darkMode }"
         style="overflow: visible;">
        <div class="flex justify-between items-center space-x-4 py-4 lg:py-0">
            <div>
                <h2
                    class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl"
                >
                    Account Management
                </h2>
                {{ breadcrumb(
                    [
                        {'name': 'Account Management', 'url': path('app_admin_account_list_index'), 'sidebar': 'management'},
                    ]
                ) }}
            </div>
            <a href="javascript:void(0)"
               @click="window.location.href= ('{{ path('app_admin_account_management_create') }}')"
               class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90"
            >
                <span id="list-account-create-button"
                      x-text="window.innerWidth > 992 ? 'Create Account' : 'Create'"></span>
            </a>
        </div>
        {% block data_table %}{% endblock %}
    </div>

    <!-- Modal Action -->
    <div x-data="{ showModal: false, modalData: { title: '', message: '', type: '', userId: null, action: '' } }"
         @open-action-modal.window="showModal = true; modalData = $event.detail"
         id="action-modal-container">
        <template x-teleport="#x-teleport-target">
            <div
                class="fixed inset-0 z-[100] flex flex-col items-center justify-center overflow-hidden px-4 py-6 sm:px-5"
                x-show="showModal"
                role="dialog"
                @keydown.window.escape="showModal = false">

                <div class="absolute inset-0 bg-slate-900/60 transition-opacity duration-300"
                     @click="showModal = false"
                     x-show="showModal"
                     x-transition:enter="ease-out"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"></div>

                <div
                    class="relative max-w-lg rounded-lg bg-white px-4 py-10 text-center transition-opacity duration-300 dark:bg-navy-700 sm:px-5"
                    x-show="showModal"
                    x-transition:enter="ease-out"
                    x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100"
                    x-transition:leave="ease-in"
                    x-transition:leave-start="opacity-100"
                    x-transition:leave-end="opacity-0">

                    <!-- Dynamic Icon based on action type -->
                    <div x-show="modalData.type === 'success'" class="inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="inline size-28 text-success" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>

                    <div x-show="modalData.type === 'warning'" class="inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="inline size-28 text-warning" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>

                    <div x-show="modalData.type === 'danger'" class="inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="inline size-28 text-error" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>

                    <div x-show="modalData.type === 'info'" class="inline-block">
                        <svg xmlns="http://www.w3.org/2000/svg" class="inline size-28 text-info" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>

                    <div class="mt-4">
                        <h2 class="text-2xl text-slate-700 dark:text-navy-100" x-text="modalData.title"></h2>
                        <p class="mt-2" x-html="modalData.message"></p>

                        <!-- Action Buttons -->
                        <div class="mt-6 flex justify-center space-x-3">
                            <!-- Cancel Button (always visible) -->
                            <button @click="showModal = false"
                                    class="btn bg-slate-150 font-medium text-slate-800 hover:bg-slate-200 focus:bg-slate-200 active:bg-slate-200/80 dark:bg-navy-500 dark:text-navy-50 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">
                                Cancel
                            </button>

                            <!-- Confirm Button (conditional) -->
                            <button x-show="modalData.action && modalData.action !== 'renew'"
                                    @click="executeAction(modalData.action, modalData.userId); showModal = false"
                                    class="btn font-medium text-white"
                                    :class="{
                                    'bg-success hover:bg-success-focus focus:bg-success-focus active:bg-success-focus/90': modalData.type === 'success',
                                    'bg-warning hover:bg-warning-focus focus:bg-warning-focus active:bg-warning-focus/90': modalData.type === 'warning',
                                    'bg-error hover:bg-error-focus focus:bg-error-focus active:bg-error-focus/90': modalData.type === 'danger',
                                    'bg-info hover:bg-info-focus focus:bg-info-focus active:bg-info-focus/90': modalData.type === 'info'
                                }"
                                    x-text="getConfirmButtonText(modalData.action)">
                            </button>

                            <!-- Special Renew Button -->
                            <button x-show="modalData.action === 'renew'"
                                    @click="executeRenewAction(modalData.userId); showModal = false"
                                    class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90">
                                Proceed to Renew
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Teleport target for modals -->
    <div id="x-teleport-target"></div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {% block account_list_tabulator %}{% endblock %}
{% endblock %}

