<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class UserConstant.
 *
 * This class holds all constant values related to the User entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing user-specific data.
 */
final class UserConstant
{

    final public const string ROLE_USER = 'ROLE_USER';

    final public const string ROLE_ADMIN = 'ROLE_ADMIN';

    final public const string ROLE_CLIENT = 'ROLE_CLIENT';

    final public const string ROLE_RESELLER = 'ROLE_RESELLER';

    final public const string ROLE_PARTNER = 'ROLE_PARTNER';

    final public const array ROLES
        = [
            self::ROLE_USER     => 'User',
            self::ROLE_ADMIN    => 'Admin',
            self::ROLE_CLIENT   => 'Client',
            self::ROLE_RESELLER => 'Reseller',
            self::ROLE_PARTNER  => 'Partner',
        ];

    final public const int STATUS_INACTIVE = 0;

    final public const int STATUS_ACTIVE = 1;

    final public const int STATUS_BLOCKED = 2;

    final public const int STATUS_DELETED = 3;

    final public const array STATUS
        = [
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_ACTIVE   => 'Active',
            self::STATUS_BLOCKED  => 'Blocked',
            self::STATUS_DELETED  => 'Deleted',
        ];

}
