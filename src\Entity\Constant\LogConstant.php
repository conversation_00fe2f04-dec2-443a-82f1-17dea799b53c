<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class LogConstant.
 *
 * This class defines constants for log levels and their corresponding names.
 * It is used to standardize log level representation across the application.
 */
final class LogConstant
{

    final public const int LOG_LEVEL_DEBUG = 100;

    final public const int LOG_LEVEL_INFO = 200;

    final public const int LOG_LEVEL_NOTICE = 250;

    final public const int LOG_LEVEL_WARNING = 300;

    final public const int LOG_LEVEL_ERROR = 400;

    final public const int LOG_LEVEL_CRITICAL = 500;

    final public const int LOG_LEVEL_ALERT = 550;

    final public const int LOG_LEVEL_EMERGENCY = 600;

    final public const string LOG_LEVEL_DEBUG_NAME = 'DEBUG';

    final public const string LOG_LEVEL_INFO_NAME = 'INFO';

    final public const string LOG_LEVEL_NOTICE_NAME = 'NOTICE';

    final public const string LOG_LEVEL_WARNING_NAME = 'WARNING';

    final public const string LOG_LEVEL_ERROR_NAME = 'ERROR';

    final public const string LOG_LEVEL_CRITICAL_NAME = 'CRITICAL';

    final public const string LOG_LEVEL_ALERT_NAME = 'ALERT';

    final public const string LOG_LEVEL_EMERGENCY_NAME = 'EMERGENCY';

    final public const array LOG_LEVELS
        = [
            self::LOG_LEVEL_DEBUG     => self::LOG_LEVEL_DEBUG_NAME,
            self::LOG_LEVEL_INFO      => self::LOG_LEVEL_INFO_NAME,
            self::LOG_LEVEL_NOTICE    => self::LOG_LEVEL_NOTICE_NAME,
            self::LOG_LEVEL_WARNING   => self::LOG_LEVEL_WARNING_NAME,
            self::LOG_LEVEL_ERROR     => self::LOG_LEVEL_ERROR_NAME,
            self::LOG_LEVEL_CRITICAL  => self::LOG_LEVEL_CRITICAL_NAME,
            self::LOG_LEVEL_ALERT     => self::LOG_LEVEL_ALERT_NAME,
            self::LOG_LEVEL_EMERGENCY => self::LOG_LEVEL_EMERGENCY_NAME,
        ];

}
