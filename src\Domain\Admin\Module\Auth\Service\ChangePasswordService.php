<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    01:08
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Service;

use App\Domain\Admin\Module\Auth\Contract\ChangePasswordServiceInterface;
use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * Class ChangePasswordService.
 *
 * This service is responsible for changing the password of an admin account.
 * It interacts with the AdminAccountRepository to perform the password change operation.
 */
class ChangePasswordService implements ChangePasswordServiceInterface
{
    private UserRepository $userRepository;
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(
        UserRepository $userRepository,
        UserPasswordHasherInterface $passwordHasher
    ) {
        $this->userRepository = $userRepository;
        $this->passwordHasher = $passwordHasher;
    }

    /**
     * @inheritDoc
     */
    public function changePassword(User $user, string $password): bool
    {
        $user->loadCredentials()->setPassword($this->passwordHasher->hashPassword($user, $password));

        if ($this->userRepository->save($user)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function verifyAndChangePassword(User $user, ?string $currentPassword, ?string $newPassword): bool
    {
        if ($currentPassword && !$this->passwordHasher->isPasswordValid($user, $currentPassword)) {
            return false; // Current password is invalid
        }

        if ($newPassword) {
            $user->loadCredentials()->setPassword($this->passwordHasher->hashPassword($user, $newPassword));
        }

        return $this->userRepository->save($user);
    }

}
