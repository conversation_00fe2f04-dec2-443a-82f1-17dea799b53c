<?php

/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/5/2025
 * @time 7:40 PM
 */
declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Domain\Admin\Module\AccountManagement\Contract\UserStatusServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\UserStatus\UserStatusRequestDTO;
use App\Exception\LogicalException;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/manage/account/list', name: 'app_admin_account_status_')]
class UserStatusController extends AdminController
{

    public function __construct(
        private readonly UserStatusServiceInterface $userStatusService,
    ) {
        parent::__construct();
    }

    /**
     * Activate a specific user account.
     *
     * Accepts a POST request with user ID, and attempts to activate the account.
     * Displays SweetAlert notifications based on success/failure.
     */
    #[Route('/activate/{id}', name: 'activate', methods: ['POST'])]
    public function activate(int $id): JsonResponse
    {
        try {
            $requestDTO = new UserStatusRequestDTO();

            $requestDTO->userId = $id;

            $user = $this->userStatusService->activateUser($requestDTO);

            flash()->success('User activated successfully');

            return $this->json(['success' => $user]);
        } catch (LogicalException $e) {
            flash()->error($e->getMessage());

            return $this->json(['success' => false]);
        } catch (Exception) {
            flash()->error('An error occurred while processing your request');

            return $this->json(['success' => false]);
        }
    }

    /**
     * Deactivate a specific user account and optionally its children.
     *
     * Accepts a POST request with user ID, and deactivates the user and all descendants.
     * Displays SweetAlert based on operation result.
     */
    #[Route('/deactivate/{id}', name: 'deactivate', methods: ['POST'])]
    public function deactivate(int $id): JsonResponse
    {
        try {
            $requestDTO = new UserStatusRequestDTO();

            $requestDTO->userId = $id;

            $user = $this->userStatusService->deactivateUser($requestDTO);

            flash()->success('User deactivated successfully');

            return $this->json(['success' => $user]);
        } catch (LogicalException $e) {
            flash()->error($e->getMessage());

            return $this->json(['success' => false]);
        } catch (Exception) {
            flash()->error('An error occurred while processing your request');

            return $this->json(['success' => false]);
        }
    }

    /**
     * Restore a soft-deleted user account and optionally its children.
     *
     * Accepts POST requests and attempts to restore the account.
     * Will respect business rules, such as 30-day restoration window.
     */
    #[Route('/restore/{id}', name: 'restore', methods: ['POST'])]
    public function restore(int $id): JsonResponse
    {
        try {
            $requestDTO = new UserStatusRequestDTO();

            $requestDTO->userId = $id;

            $user = $this->userStatusService->restoreUser($requestDTO);

            flash()->success('User restored successfully');

            return $this->json(['success' => $user]);
        } catch (LogicalException $e) {
            flash()->error($e->getMessage());

            return $this->json(['success' => false]);
        } catch (Exception) {
            flash()->error('An error occurred while processing your request');

            return $this->json(['success' => false]);
        }
    }

}
