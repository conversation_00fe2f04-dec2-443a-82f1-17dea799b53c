{% extends 'domains/admin/shared/layouts/main.html.twig' %}

{% block title %}
    User Account Detail
{% endblock %}

{% block content %}
    <div class="col-span-12 lg:w-full flex items-center space-x-4 py-5 lg:py-6">
        <h2
                class="text-xl font-medium text-slate-800 dark:text-navy-50 lg:text-2xl"
        >
            User Account Detail
        </h2>
        <div class="hidden h-full py-1 sm:flex">
            <div
                    class="h-full w-px bg-slate-300 dark:bg-navy-600"
            ></div>
        </div>
        {{ breadcrumb(
            [
                {'name': 'Account Management', 'url': path('app_admin_account_list_index'), sidebar: 'management'},
                {'name': 'Account Detail ' ~ id }
            ]
        ) }}
    </div>

    <div class="col-span-12 pb-6">
        <div class="card rounded-lg custom-card">
            <div class="profile-banner-card">
                <img
                        class="w-full rounded-t-lg object-cover object-center"
                        src="{{ asset('resources/theme/images/object/object-13.jpg') }}"
                        alt="img"
                        style="height: 23vh;"
                />
            </div>
            <div class="p-4 pb-0 relative">
                <div class="grid grid-cols-12 gap-4 transition-all duration-[.25s] lg:gap-6 profile-content">
                    <div class="col-span-12 lg:col-span-3">
                        <div class="card custom-card overflow-hidden border">
                            <div class="border-b border-dashed bottom-border-dashed dark:border-white/10 p-6">
                                <div class="flex flex-col items-center">
                                    <!-- Avatar with online status -->
                                    <div class="relative mb-3">
                                        <div class="inline-block rounded-full p-1 bg-primary-70">
                                            {% if userProfile.avatar is not empty %}
                                                <img
                                                        src="{{ userProfile.avatar }}"
                                                        alt="avatar"
                                                        class="w-24 h-24 rounded-full object-cover"
                                                />
                                            {% else %}
                                                <img
                                                        src="{{ asset('resources/theme/images/avatar/avatar-blank.jpg') }}"
                                                        alt="avatar"
                                                        class="w-24 h-24 rounded-full object-cover"
                                                />
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- User info -->
                                    <h5 class="font-semibold text-slate-800 dark:text-white text-lg mb-1">
                                        {{ userProfile.firstName|default('Guest') }} {{ userProfile.lastName|default('Guest') }}
                                    </h5>
                                    <span class="block font-medium text-gray-500 mb-2">
                                        {{ accountInfo.roles|map(role => role|replace({'ROLE_': ''}))|join(', ')|capitalize|default('Guest') }}
                                    </span>
                                </div>
                            </div>

                            <div class="p-3 pb-1 flex flex-wrap justify-between">
                                <div class="font-medium text-base text-primary">
                                    Basic Info :
                                </div>
                            </div>

                            <div class="p-0">
                                <ul class="divide-y-0">
                                    <li class="py-2 px-4">
                                        <div>
                                            <span class="font-medium text-slate-800 dark:text-white mr-2">Bio :</span>
                                            <span class="text-gray-500">
                                                {{ userProfile.biography|default('This is a biography.') }}
                                            </span>
                                        </div>
                                    </li>
                                    <li class="py-2 px-4">
                                        <div>
                                            <span class="font-medium text-slate-800 dark:text-white mr-2">Date of Birth :</span>
                                            <span class="text-gray-500">
                                                {{ userProfile.dob|default('xxxx-xx-xx') }}
                                            </span>
                                        </div>
                                    </li>
                                    <li class="py-2 px-4">
                                        <div>
                                            <span
                                                    class="font-medium text-slate-800 dark:text-white mr-2">Gender :</span>
                                            <span id="gender" class="text-gray-500">
                                            </span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-12 lg:col-span-9">
                        <div class="card custom-card overflow-hidden border">
                            <div x-data="{activeTab:'tabInfo'}" class="tabs flex flex-col p-6">
                                <div
                                        class="is-scrollbar-hidden overflow-x-auto rounded-lg text-slate-600 dark:text-navy-200"
                                >
                                    <div class="tabs-list flex px-1.5 py-1 pb-3 gap-3">
                                        <button
                                                @click="activeTab = 'tabInfo'"
                                                :class="activeTab === 'tabInfo' ? 'shadow-sm text-white bg-primary' : 'text-primary'"
                                                class="btn shrink-0 px-3 py-1.5 font-medium"
                                        >
                                            Information
                                        </button>
                                        <button
                                                @click="activeTab = 'tabType'"
                                                :class="activeTab === 'tabType' ? 'shadow-sm text-white bg-primary' : 'text-primary'"
                                                class="btn shrink-0 px-3 py-1.5 font-medium"
                                        >
                                            User Type & Last Purchased
                                        </button>
                                        <button
                                                @click="activeTab = 'tabWallet'"
                                                :class="activeTab === 'tabWallet' ? 'shadow-sm text-white bg-primary' : 'text-primary'"
                                                class="btn shrink-0 px-3 py-1.5 font-medium"
                                        >
                                            Wallet & Transactions
                                        </button>
                                    </div>
                                </div>
                                <div class="tab-content mt-4">
                                    <div
                                            x-show="activeTab === 'tabInfo'"
                                            x-transition:enter="transition-all duration-500 easy-in-out"
                                            x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                            x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]"
                                    >
                                        <div>
                                            <ul class="border-default rounded-lg">
                                                <li class="p-3">
                                                    <span
                                                            class="bottom-border-solid pb-3 font-medium text-base text-slate-800 dark:text-white block mb-3">
                                                        <span class="me-1">✨</span>
                                                        Information :
                                                    </span>
                                                    <div class="px-4 text-gray-500">
                                                        <div class="grid grid-cols-12 gap-4">
                                                            <div class="col-span-12 lg:col-span-6">
                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-parent-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Parent :</span>
                                                                    <span class="text-gray-500">
                                                                {{ accountInfo.parent|default('No parent assigned') }}
                                                            </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-user-3-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Username :</span>
                                                                    <span class="text-gray-500">
                                                                {{ accountInfo.username|default('No username assigned') }}
                                                            </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-mail-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Email :</span>
                                                                    <span class="text-gray-500">
                                                                {{ accountInfo.email|default('No email assigned') }}
                                                            </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-shield-user-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Role :</span>
                                                                    <span id="role" class="text-gray-500">
                                                            </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-computer-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Registration IP :</span>
                                                                    <span class="text-gray-500">
                                                                {{ accountInfo.registrationIp|default('No registration ip assigned') }}
                                                            </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                            <span
                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                <i class="ri-checkbox-circle-line align-middle"></i>
                                                            </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Status :</span>
                                                                    <span id="status" class="text-gray-500">
                                                            </span>
                                                                </p>
                                                            </div>
                                                            <div class="col-span-12 lg:col-span-6">
                                                                <p class="flex items-center mb-3">
                                                                    <span
                                                                            class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                        <i class="ri-login-circle-line align-middle"></i>
                                                                    </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Last Login At :</span>
                                                                    <span class="text-gray-500">
                                                                        {% if accountInfo.lastLoginAt is not empty %}
                                                                            {{ accountInfo.lastLoginAt|date('Y-m-d H:i:s') }}
                                                                        {% else %}
                                                                            No last login date assigned.
                                                                        {% endif %}
                                                                    </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                                    <span
                                                                            class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                        <i class="ri-forbid-line align-middle"></i>
                                                                    </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Blocked At :</span>
                                                                    <span class="text-gray-500">
                                                                        {% if accountInfo.blockedAt is not empty %}
                                                                            {{ accountInfo.blockedAt|date('Y-m-d H:i:s') }}
                                                                        {% else %}
                                                                            No blocked date assigned.
                                                                        {% endif %}
                                                                    </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                                    <span
                                                                            class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                        <i class="ri-delete-bin-line align-middle"></i>
                                                                    </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Deleted At :</span>
                                                                    <span class="text-gray-500">
                                                                        {% if accountInfo.deletedAt is not empty %}
                                                                            {{ accountInfo.deletedAt|date('Y-m-d H:i:s') }}
                                                                        {% else %}
                                                                            No deleted date assigned.
                                                                        {% endif %}
                                                                    </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                                    <span
                                                                            class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                        <i class="ri-add-circle-line align-middle"></i>
                                                                    </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Created At :</span>
                                                                    <span class="text-gray-500">
                                                                        {% if accountInfo.createdAt is not empty %}
                                                                            {{ accountInfo.createdAt|date('Y-m-d H:i:s') }}
                                                                        {% else %}
                                                                            No created date assigned.
                                                                        {% endif %}
                                                                    </span>
                                                                </p>

                                                                <p class="flex items-center mb-3">
                                                                    <span
                                                                            class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                        <i class="ri-refresh-line align-middle"></i>
                                                                    </span>
                                                                    <span
                                                                            class="font-medium text-slate-800 dark:text-white mr-2">Updated At :</span>
                                                                    <span class="text-gray-500">
                                                                        {% if accountInfo.updatedAt is not empty %}
                                                                            {{ accountInfo.updatedAt|date('Y-m-d H:i:s') }}
                                                                        {% else %}
                                                                            No updated date assigned.
                                                                        {% endif %}
                                                                    </span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div
                                            x-show="activeTab === 'tabType'"
                                            x-transition:enter="transition-all duration-500 easy-in-out"
                                            x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                            x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]"
                                    >
                                        <div>
                                            <ul class="border-default rounded-lg">
                                                <li class="bottom-border-solid p-3">
                                                    <span
                                                            class="pb-3 font-medium text-base text-slate-800 dark:text-white block mb-3">
                                                        <span class="me-1">🌟</span>
                                                        User Type : <span id="role2"></span>
                                                    </span>
                                                    {% if userTypeSpecificData is not empty %}
                                                        {% if 'ROLE_CLIENT' in accountInfo.roles %}
                                                            <div class="px-4 text-gray-500">
                                                                <div class="grid grid-cols-12 gap-4">
                                                                    <div class="col-span-12 lg:col-span-6">
                                                                        <p class="flex items-center mb-3">
                                                                            <span
                                                                                    class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                                <i class="ri-repeat-line align-middle"></i>
                                                                            </span>
                                                                            <span
                                                                                    class="font-medium text-slate-800 dark:text-white mr-2">Is Auto Renew :</span>
                                                                            <span class="text-gray-500">
                                                                                {{ userTypeSpecificData.isAutoRenew ? 'Yes' : 'No' }}
                                                                            </span>
                                                                        </p>
                                                                    </div>
                                                                    <div class="col-span-12 lg:col-span-6">
                                                                        <p class="flex items-center mb-3">
                                                                        <span
                                                                                class="w-8 h-8 rounded-full flex items-center justify-center text-primary bg-primary-icon p-1 mr-2">
                                                                            <i class="ri-history-line align-middle"></i>
                                                                        </span>
                                                                            <span
                                                                                    class="font-medium text-slate-800 dark:text-white mr-2">Last Renew At :</span>
                                                                            <span class="text-gray-500">
                                                                            {{ userTypeSpecificData.lastRenewAt|date('Y-m-d H:i:s')|default('-') }}
                                                                        </span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    {% else %}
                                                        <div class="px-4 text-gray-500">
                                                            <p class="text-center">No user-type-specific data found.</p>
                                                        </div>
                                                    {% endif %}
                                                </li>

                                                <li class="p-3">
                                                    <span
                                                            class="pb-3 font-medium text-base text-slate-800 dark:text-white block mb-3">
                                                        <span class="me-1">💫</span>
                                                        Last Purchased :
                                                    </span>
                                                    {% if lastItemPurchased is not empty %}
                                                        {% if 'ROLE_CLIENT' in accountInfo.roles %}
                                                            <div class="px-4 text-gray-500">
                                                                <div class="flex flex-col">
                                                                    <img
                                                                            class="h-44 rounded-2xl object-cover object-center"
                                                                            src="{{ asset('resources/theme/images/object/credit-package.png') }}"
                                                                            alt="img"
                                                                    />
                                                                    <div class="card -mt-8 grow rounded-2xl p-4">
                                                                        <div>
                                                                            <span
                                                                                    class="text-sm-plus font-medium text-slate-700 line-clamp-1 hover:text-primary focus:text-primary dark:text-navy-100 dark:hover:text-accent-light dark:focus:text-accent-light"
                                                                            >
                                                                                {{ lastItemPurchased.name|default('No name assigned') }}
                                                                            </span>
                                                                            <span class="text-gray-500">
                                                                                <i class="ri-command-line mx-1"></i>{{ lastItemPurchased.shortcut|default("No shortcut assigned") }}
                                                                            </span>
                                                                        </div>
                                                                        <p class="mt-2 grow line-clamp-3">
                                                                            {{ lastItemPurchased.description|default('This is a description.') }}
                                                                        </p>
                                                                        <div
                                                                                class="mt-4 flex items-center justify-between">
                                                                            <a
                                                                                    href="#"
                                                                                    class="flex items-center space-x-2 text-xs hover:text-slate-800 dark:hover:text-navy-100"
                                                                            >
                                                                                <i class="ri-price-tag-3-line mr-1"></i>
                                                                                <span
                                                                                        class="line-clamp-1">{{ lastItemPurchased.price|number_format(2, '.', ',')|default('0.00') }}</span>
                                                                            </a>
                                                                            <p
                                                                                    class="flex shrink-0 items-center space-x-1.5 text-slate-400 dark:text-navy-300"
                                                                            >
                                                                                <i class="ri-bank-card-line mr-1"></i>
                                                                                <span
                                                                                        class="text-xs">{{ lastItemPurchased.credit|number_format(2, '.', ',')|default('0.00') }}</span>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    {% else %}
                                                        <div class="px-4 text-gray-500">
                                                            <p class="text-center">No last purchased item found.</p>
                                                        </div>
                                                    {% endif %}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div
                                            x-show="activeTab === 'tabWallet'"
                                            x-transition:enter="transition-all duration-500 easy-in-out"
                                            x-transition:enter-start="opacity-0 [transform:translate3d(1rem,0,0)]"
                                            x-transition:enter-end="opacity-100 [transform:translate3d(0,0,0)]"
                                    >
                                        <div>
                                            <ul class="border-default rounded-lg">
                                                <li class="bottom-border-solid p-3">
                                                    <span
                                                            class="pb-3 font-medium text-base text-slate-800 dark:text-white block mb-3">
                                                        <span class="me-1">💰</span>
                                                        Wallet :
                                                    </span>

                                                    {% if wallet is not empty %}
                                                        <div class="grid grid-cols-12 gap-4 px-5">
                                                            <div class="col-span-12 lg:col-span-4">
                                                                <div
                                                                        class="relative flex flex-col overflow-hidden rounded-lg bg-gradient p-3.5"
                                                                >
                                                                    <p class="text-xs uppercase text-sky-100">
                                                                        Total Credit
                                                                    </p>
                                                                    <div
                                                                            class="flex items-end justify-between space-x-2"
                                                                    >
                                                                        <p
                                                                                class="mt-4 text-2xl font-medium text-white"
                                                                        >
                                                                            {{ wallet.credit|number_format(2, '.', ',')|default('0.00') }}
                                                                        </p>
                                                                    </div>
                                                                    <div
                                                                            class="mask is-reuleaux-triangle absolute top-0 right-0 -m-3 size-16 bg-white/20"
                                                                    ></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% else %}
                                                        <div class="px-4 text-gray-500">
                                                            <p class="text-center">No wallet information was found.</p>
                                                        </div>
                                                    {% endif %}
                                                </li>

                                                <li class="p-3">
                                                    <span
                                                            class="pb-3 font-medium text-base text-slate-800 dark:text-white block mb-3">
                                                        <span class="me-1">📑</span>
                                                        Transactions :
                                                    </span>

                                                    {% if transactions is empty %}
                                                        <div class="px-4 text-gray-500">
                                                            <p class="text-center">No transactions found.</p>
                                                        </div>
                                                    {% else %}
                                                        <div class="px-4 text-gray-500 overflow-y-auto"
                                                             style="height: 50vh;">
                                                            <ol
                                                                    class="timeline line-space px-4 [--size:1.5rem] sm:px-5"
                                                            >
                                                                {% for transaction in transactions %}
                                                                    <li class="timeline-item">
                                                                        <div
                                                                                class="timeline-item-point rounded-full border border-current bg-white text-primary dark:bg-navy-700 dark:text-primary"
                                                                        >
                                                                    <span class="align-middle">
                                                                        <i class="ri-exchange-dollar-line"></i>
                                                                    </span>
                                                                        </div>
                                                                        <div
                                                                                class="timeline-item-content flex-1 pl-4 sm:pl-8"
                                                                        >
                                                                            <div
                                                                                    class="flex flex-col justify-between pb-2 sm:flex-row sm:pb-0"
                                                                            >
                                                                                <p
                                                                                        class="pb-2 font-medium leading-none text-slate-600 dark:text-navy-100 sm:pb-0"
                                                                                >
                                                                                    {% if transaction.type == 0 %}
                                                                                        Increase
                                                                                    {% elseif transaction.type == 1 %}
                                                                                        Decrease
                                                                                    {% else %}
                                                                                        Unknown
                                                                                    {% endif %}
                                                                                </p>
                                                                                <span
                                                                                        class="text-xs text-slate-400 dark:text-navy-300"
                                                                                >
                                                                                {% if transaction.loggedAt is not empty %}
                                                                                    {{ transaction.loggedAt|date('Y-m-d H:i:s') }}
                                                                                {% else %}
                                                                                    No date assigned.
                                                                                {% endif %}
                                                                            </span></div>
                                                                            <p class="py-1">
                                                                                Amount: <span
                                                                                        class="text-slate-800 dark:text-navy-100">{{ transaction.amount|number_format(2, '.', ',')|default('0.00') }}</span>,
                                                                                Credit: <span
                                                                                        class="text-slate-800 dark:text-navy-100">{{ transaction.credit|number_format(2, '.', ',')|default('0.00') }}</span>
                                                                            </p>
                                                                        </div>
                                                                    </li>
                                                                {% endfor %}
                                                            </ol>
                                                        </div>
                                                    {% endif %}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mx-5 px-5 py-4 shadow-md sm:px-5 flex flex-row justify-between">
                                <div class="relative"
                                     x-data="{ showTooltip: false, canRestore: {{ accountInfo.status == 3 ? 'true' : 'false' }} }">
                                    <button
                                            class="btn min-w-[7rem] rounded-full font-medium text-white"
                                            :class="canRestore ? 'bg-success hover:bg-success-focus' : 'bg-slate-300 cursor-not-allowed'"
                                            :disabled="!canRestore"
                                            :data-user-id="{{ id }}"
                                            data-action="restore"
                                            @mouseenter="showTooltip = true"
                                            @mouseleave="showTooltip = false"
                                            onclick="toggleUserStatus(this)"
                                    >
                                        <i class="ri-refresh-line mr-1.5"></i>
                                        <span>Restore</span>
                                    </button>
                                    <div x-show="showTooltip" class="alpine-tooltip" x-cloak>
                                        To restore an account that has been soft-deleted
                                        <div
                                                class="absolute left-1/2 top-full -translate-x-1/2 border-4 border-transparent border-t-slate-700"></div>
                                    </div>
                                </div>

                                <div class="relative"
                                     x-data="{ showTooltip: false, isActive: {{ accountInfo.status == 1 ? 'true' : 'false' }} }">
                                    <button id="activechange"
                                            class="btn min-w-[7rem] rounded-full font-medium text-white"
                                            :class="isActive ? 'btn-secondary-gray' : 'bg-primary hover:bg-primary-focus'"
                                            @mouseenter="showTooltip = true"
                                            @mouseleave="showTooltip = false"
                                            :data-user-id="{{ id }}"
                                            :data-active="isActive"
                                            :data-action="isActive ? 'deactivate' : 'activate'"
                                            onclick="toggleUserStatus(this)"
                                    >
                                        <i class="mr-1.5" :class="isActive ? 'ri-toggle-fill' : 'ri-toggle-line'"></i>
                                        <span x-text="isActive ? 'Deactivate' : 'Activate'"></span>
                                    </button>
                                    <div
                                            x-show="showTooltip"
                                            class="alpine-tooltip"
                                            x-cloak
                                    >
                                        <span
                                                x-text="isActive ? 'To change the account\'s status from Active to Inactive' : 'To change the account\'s status from Inactive to Active'"></span>
                                        <div
                                                class="absolute left-1/2 top-full -translate-x-1/2 border-4 border-transparent border-t-slate-700"></div>
                                    </div>
                                </div>

                                <div class="relative" x-data="{ showTooltip: false, isBlocked: false }">
                                    <button
                                            class="btn min-w-[7rem] rounded-full font-medium text-white"
                                            :class="isBlocked ? 'bg-info hover:bg-info-focus' : 'bg-error hover:bg-error-focus'"
                                            @mouseenter="showTooltip = true"
                                            @mouseleave="showTooltip = false"
                                            @click="isBlocked = !isBlocked"
                                    >
                                        <i class="mr-1.5"
                                           :class="isBlocked ? 'ri-checkbox-circle-line' : 'ri-forbid-line'"></i>
                                        <span x-text="isBlocked ? 'Unblock' : 'Block'"></span>
                                    </button>
                                    <div
                                            x-show="showTooltip"
                                            class="alpine-tooltip"
                                            x-cloak
                                    >
                                        <span
                                                x-text="isBlocked ? 'To unblock a previously blocked account' : 'To block the account for policy violations'"></span>
                                        <div
                                                class="absolute left-1/2 top-full -translate-x-1/2 border-4 border-transparent border-t-slate-700"></div>
                                    </div>
                                </div>

                                <div class="relative" x-data="{ showTooltip: false }">
                                    <button
                                            @click.prevent="navigateToPage('{{ path('app_admin_account_list_index') }}', 'management')"
                                            class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90"
                                            @mouseenter="showTooltip = true"
                                            @mouseleave="showTooltip = false"
                                    >
                                        <i class="ri-arrow-left-line mr-1.5"></i>
                                        <span>Back</span>
                                    </button>
                                    <div
                                            x-show="showTooltip"
                                            class="alpine-tooltip"
                                            x-cloak
                                    >
                                        To navigate back to the previous page
                                        <div
                                                class="absolute left-1/2 top-full -translate-x-1/2 border-4 border-transparent border-t-slate-700"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('resources/domains/admin/modules/account-management/css/detail.css') }}">
    <script src="{{ asset('resources/domains/admin/modules/account-management/js/detail.js') }}"></script>
    <script src="{{ asset('resources/domains/admin/modules/account-management/js/toggleUserStatus.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('role').innerHTML += showRole('{{ accountInfo.roles|first|default('Unknown') }}')
            document.getElementById('role2').innerHTML += showRole('{{ accountInfo.roles|first|default('Unknown') }}')
            document.getElementById('status').innerHTML += showStatus({{ accountInfo.status|default('-1') }})
            document.getElementById('gender').innerHTML += showGender({{ userProfile.gender|default('-1') }})
        })
    </script>
{% endblock %}
