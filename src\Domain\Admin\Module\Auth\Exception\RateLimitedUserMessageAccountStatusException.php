<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    07:53
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Exception;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAccountStatusException;

/**
 * Class RateLimitedUserMessageAccountStatusException.
 *
 * This exception is thrown when a user has exceeded the rate limit for login attempts.
 * It extends the CustomUserMessageAccountStatusException to provide a user-friendly message.
 */
class RateLimitedUserMessageAccountStatusException extends CustomUserMessageAccountStatusException
{
}
