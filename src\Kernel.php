<?php

declare(strict_types=1);

namespace App;

use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;

/**
 * Class Kernel.
 *
 * The Kernel is the heart of the Symfony system.
 * It manages an environment made of bundles.
 * Environment names must always start with a letter, and they must only contain letters and numbers.
 */
class Kernel extends BaseKernel
{

    use MicroKernelTrait;

    public function __construct($environment, $debug)
    {
        date_default_timezone_set('UTC');

        parent::__construct($environment, $debug);
    }

}
