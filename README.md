# Introduction

High-performance and fully flexible automated e-commerce platform.

# Prerequisites

Docker Engine (version 20.10+)
Docker Compose (version 2.0+)
Git
Basic knowledge of Docker and Symfony

# Installation Steps

- Clone the Repository

```bash
# Clone the repository
<NAME_EMAIL>:evostack-soft/zenshop.git
# Cd to the repository
cd zenshop
# Make sure you are on the staging branch
git checkout staging
```

- Configure Local Environment

Add the following entry to your hosts file (/etc/hosts on Linux/Mac or C:\Windows\System32\drivers\etc\hosts on
Windows):

```bash
127.0.0.1 zenshop.local
```

- Set Up Docker Configuration

Copy the .env.example to .env

```bash
cp -r .env.example .env
```

Copy necessary files

```bash
cp -r docker/dev/mariadb docker/mariadb
cp -r docker/dev/nginx docker/nginx
cp -r docker/dev/redis docker/redis
cp -r docker/dev/docker-compose.yml docker-compose.yml
cp -r docker/Dockerfile.example Dockerfile
```

- Build and Start the Docker Environment

```bash
docker-compose up -d --build
```

This command builds the custom PHP image and starts all services defined in the docker-compose.yml file.

- Install Symfony Dependencies and set proper permissions

If you use Windows Server and got error, try to access the `app container` via Docker Desktop then run the command.

```bash
# Install all dependencies
docker-compose exec app composer install
# Set proper permissions
docker-compose exec app chown -R www-data:www-data var
# Clear the cache
docker-compose exec app php bin/console cache:clear
```

# Usage

## Accessing the Application

After completing the installation, you can access the ZenShop application at:

- Web Interface: http://zenshop.local:8081

## Docker Services

| Service | Description                       | External Port | Internal Port |
|---------|-----------------------------------|---------------|---------------|
| nginx   | Nginx web server                  | 8081          | 80            |
| php     | PHP 8.4 with necessary extensions | N/A           | 9000          |
| mariadb | MariaDB 11.x database server      | 3307          | 3306          |
| redis   | Redis latest version              | 6380          | 6379          |

## Connecting to Services

### Database (MariaDB)

From your host machine:

```bash
mysql -h <db_host> -P 3307 -u <db_user> -p<db_password> <db_name>
```

Using external tools like DBeaver:

Host: <db_host>

Port: <db_port>

Username: <db_user>

Password: <db_password>

Database: <db_name>

### Redis

From your host machine:

```bash
redis-cli -h <db_host> -p 6380
```

## Common Docker Commands

### Container Management

```bash
# View running containers
docker-compose ps

# Start all services
docker-compose start

# Stop all services
docker-compose stop

# Restart all services
docker-compose restart

# Stop and remove containers, networks
docker-compose down

# Stop and remove containers, networks, volumes
docker-compose down -v
```

### Logs

```bash
# View logs for all services
docker-compose logs

# View logs for a specific service
docker-compose logs nginx

# Follow logs in real-time
docker-compose logs -f app
```

### Executing Commands

```bash
# Run Symfony console commands
docker-compose exec app php bin/console [command]

# Access APP container shell
docker-compose exec app bash

# Install Composer dependencies
docker-compose exec app composer install
```

# Performance Optimization

The Docker setup has been optimized for performance by:

1. Using volume mounting strategies with :cached and :delegated options
2. Keeping the vendor and cache directories within the container
3. Setting up Composer cache for faster package installations

# Customization

Changing Port Mappings
If you need to run multiple Docker applications simultaneously, you can modify the port mappings in the
.env.local file:

Example: Change Nginx port from 8081 to 8082

```bash
###> Docker ###
DOCKER_NGINX_PORT=8081
###< Docker ###
```

# Modifying PHP Configuration

To customize PHP settings, create a custom ini file:

```bash
mkdir -p docker/php/conf.d
echo "memory_limit = 512M" > docker/php/conf.d/custom.ini
```

Then update the PHP service in docker-compose.yml to include this volume:

```bash
php:
  volumes:
    - ./docker/php/conf.d:/usr/local/etc/php/conf.d/custom.ini
```

# Troubleshooting

## Connection Issues

If you cannot connect to the web application:

1. Verify all containers are running: docker-compose ps
2. Check Nginx logs: docker-compose logs nginx
3. Ensure the host entry is correctly set in your hosts file
4. Confirm the ports are not being used by other applications

## Database Connection Errors

If Symfony cannot connect to the database:

1. Verify the database container is running: docker-compose ps mariadb
2. Check the database logs: docker-compose logs mariadb
3. Ensure the database name, username, and password in your Symfony .env file match those in the docker-compose.yml

## Permission Issues

If you encounter permission errors:

```bash
docker-compose exec app chown -R www-data:www-data /var/www/zenshop
```

# Security Considerations

1. In production environments, change the default database credentials
2. Disable Xdebug in production
3. Consider implementing Docker secrets for sensitive information
