<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    05/06/2025
 * @time    15:38
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Data Transfer Object (DTO) for user profile information.
 *
 * This DTO encapsulates personal details of a user, such as their name,
 * date of birth, gender, and avatar, typically used for displaying and
 * managing user profiles in an application.
 */
final class UserProfileDTO
{
    public ?string $avatar;
    public ?string $firstName;
    public ?string $lastName;
    public ?string $dob;
    public ?string $biography;
    public ?int $gender;
}
