<?php

declare(strict_types=1);

namespace App\Repository;

use App\Base\BaseRepository;
use App\Entity\UserWalletTransaction;

/**
 * Class UserWalletTransactionModelRepository.
 *
 * This class is responsible for handling user-wallet-transaction-related database operations.
 */
class UserWalletTransactionRepository extends BaseRepository
{

    protected function loadEntity(): string
    {
        return UserWalletTransaction::class;
    }

}
