<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/9/2025
 * @time 6:45 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Contract;

use App\Domain\Admin\Module\AccountManagement\DTO\CreateNewAccountDTO;

/**
 * Interface CreateNewAccountServiceInterface
 *
 * Defines the contract for creating a new user account.
 */
interface CreateNewAccountServiceInterface
{

    /**
     * Creates a new user account.
     *
     * @param  CreateNewAccountDTO  $dto
     *
     * @return bool
     */
    public function createNewAccount(CreateNewAccountDTO $dto): bool;

    /**
     * Check unique for email.
     *
     * @param  string  $email
     *
     * @return bool
     */
    public function checkUniqueEmail(string $email): bool;

}
