const GENDERS = {
    0: 'Male',
    1: 'Female',
    2: 'Other',
}

// Role definitions
const ROLES = {
    'ROLE_ADMIN': {name: 'Admin', priority: 5, class: 'badge-admin'},
    'ROLE_RESELLER': {name: 'Reseller', priority: 4, class: 'badge-reseller'},
    'ROLE_CLIENT': {name: 'Client', priority: 3, class: 'badge-client'},
    'ROLE_PARTNER': {name: 'Partner', priority: 2, class: 'badge-partner'},
    'ROLE_USER': {name: 'User', priority: 1, class: 'badge-user'},
}

// Status definitions
const STATUS = {
    0: {
        name: 'Inactive',
        class: 'badge rounded-full bg-slate-150 text-slate-800 dark:bg-navy-500 dark:text-navy-100'
    },
    1: {name: 'Active', class: 'badge rounded-full bg-success text-white'},
    2: {name: 'Blocked', class: 'badge rounded-full bg-warning text-white'},
    3: {name: 'Deleted', class: 'badge rounded-full bg-error text-white'},
}

function showStatus(status) {
    const statusInfo = STATUS[status];

    if (statusInfo || status === 0) {
        return `<span class="${STATUS[status].class}">${STATUS[status].name}</span>`;
    }

    return `<span class="badge rounded-full bg-gray-300 text-gray-800">Unknown</span>`;
}

function showRole(role) {
    const roleInfo = ROLES[role];

    if (roleInfo) {
        return `<span class="badge ${roleInfo.class}">${roleInfo.name}</span>`;
    }

    return `<span class="badge badge-default">Unknown</span>`;
}

function showGender(gender) {
    const genderInfo = GENDERS[gender];

    if (genderInfo || gender === 0) {
        return `${GENDERS[gender]}`;
    }

    return 'Unknown';
}
