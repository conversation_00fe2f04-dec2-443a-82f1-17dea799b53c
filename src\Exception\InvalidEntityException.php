<?php

declare(strict_types=1);

namespace App\Exception;

use InvalidArgumentException;

/**
 * Class InvalidEntityException.
 *
 * This exception is thrown when an invalid entity is encountered in the application.
 * It extends the InvalidArgumentException to indicate that the argument provided
 * does not meet the expected criteria for a valid entity.
 */
class InvalidEntityException extends InvalidArgumentException
{

}
