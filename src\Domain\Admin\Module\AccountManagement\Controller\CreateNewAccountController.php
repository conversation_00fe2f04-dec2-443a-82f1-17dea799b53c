<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    6/8/2025
 * @time    4:42 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Domain\Admin\Module\AccountManagement\Contract\CreateNewAccountServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\CreateNewAccountDTO;
use App\Domain\Admin\Module\AccountManagement\Form\CreateNewAccountFormType;
use App\Utils\Helper\NetworkHelper;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CreateNewAccountController
 *
 * Handles the user interface and request handling for creating a new account
 * in the admin panel.
 */
#[Route('/admin/manage/account', name: 'app_admin_account_management_')]
class CreateNewAccountController extends AdminController
{

    public function __construct(private readonly CreateNewAccountServiceInterface $createNewAccountService)
    {
        parent::__construct();
    }

    /**
     * Displays and processes the new account creation form.
     *
     * @param  Request  $request
     *
     * @return Response
     */
    #[Route('/create', name: 'create', methods: ['GET', 'POST'])]
    public function create(Request $request): Response
    {
        $dto = new CreateNewAccountDTO();
        $form = $this->createForm(CreateNewAccountFormType::class, $dto);
        $dto->registrationIp = NetworkHelper::realIP();
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            return $this->processFormSubmission($form, $dto);
        }

        return $this->view('/account-management/pages/create-new-account/create', [
            'form' => $form->createView(),
            'registration_ip' => $request->getClientIp(),
        ]);
    }

    /**
     * Process the form submission for account creation
     *
     * @param  \Symfony\Component\Form\FormInterface  $form
     * @param  CreateNewAccountDTO  $dto
     *
     * @return Response
     */
    private function processFormSubmission(FormInterface $form, CreateNewAccountDTO $dto): Response
    {
        $uniquenessCheckResult = $this->validateUniqueness($dto);
        if ($uniquenessCheckResult !== null) {
            return $uniquenessCheckResult;
        }

        if ($form->isValid()) {
            return $this->processValidForm($dto);
        }

        return $this->handleFormErrors($form);
    }

    /**
     * Validate email and username uniqueness
     *
     * @param  CreateNewAccountDTO  $dto
     *
     * @return Response|null Returns Response if validation fails, null if passes
     */
    private function validateUniqueness(CreateNewAccountDTO $dto): ?Response
    {
        if (!$this->createNewAccountService->checkUniqueEmail($dto->email)) {
            flash()->addError('Email already exists. Please use a different email address.');

            return $this->redirectToRoute('app_admin_account_management_create');
        }

        return null;
    }

    /**
     * Process valid form submission
     *
     * @param  CreateNewAccountDTO  $dto
     *
     * @return Response
     */
    private function processValidForm(CreateNewAccountDTO $dto): Response
    {
        $response = $this->createNewAccountService->createNewAccount($dto);

        if ($response) {
            flash()->addSuccess('Account created successfully');
        } else {
            flash()->addError('An error occurred while creating the account! Check data, Please!');
        }

        return $this->redirectToRoute('app_admin_account_management_create');
    }

    /**
     * Handle form validation errors
     *
     * @param  \Symfony\Component\Form\FormInterface  $form
     *
     * @return Response
     */
    private function handleFormErrors(FormInterface $form): Response
    {
        $errors = [];
        foreach ($form->getErrors(true) as $error) {
            $fieldName = $error->getOrigin()->getName();
            $errors[] = ucfirst($fieldName).': '.$error->getMessage();
        }

        $errorMessage = !empty($errors)
            ? 'Invalid form data. Please check your input: '.$errors[0]
            : 'Invalid form data. Please check your input.';

        flash()->addError($errorMessage);

        return $this->redirectToRoute('app_admin_account_management_create');
    }

}
