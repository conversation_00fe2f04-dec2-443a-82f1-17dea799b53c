<?php

declare(strict_types=1);

namespace App\Entity\Trait;

use Doctrine\ORM\Mapping as ORM;

/**
 * Trait HasTypeTrait.
 *
 * This trait provides any common functionality related to the type of the entity.
 */
trait HasTypeTrait
{

    #[ORM\Column(name: 'type', type: 'smallint')]
    protected int $type;

    /**
     * Check if the entity has a specific type.
     *
     * @param  int  $type  The type to check against
     *
     * @return bool True if the entity has the specified type, false otherwise
     */
    public function hasType(int $type): bool
    {
        return $this->getType() === $type;
    }

    public function getType(): int
    {
        return $this->type;
    }

    public function setType(int $type): static
    {
        $this->type = $type;

        return $this;
    }

}
