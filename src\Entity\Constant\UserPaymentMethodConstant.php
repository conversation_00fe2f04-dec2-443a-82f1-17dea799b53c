<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class UserPaymentMethodConstant.
 *
 * This class holds all constant values related to the UserPaymentMethod entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing user-payment-method-specific data.
 */
final class UserPaymentMethodConstant
{

    final public const int TYPE_CREDIT_CARD = 0;

    final public const int TYPE_VISA = 1;

    final public const int TYPE_MASTERCARD = 2;

    final public const int TYPE_PAYPAL = 3;

    final public const int TYPE_STRIPE = 4;

    final public const int TYPE_PAYONEER = 5;

    final public const int TYPE_2CHECKOUT = 6;

    final public const array TYPES
        = [
            self::TYPE_CREDIT_CARD => 'Credit Card',
            self::TYPE_VISA        => 'Visa',
            self::TYPE_MASTERCARD  => 'MasterCard',
            self::TYPE_PAYPAL      => 'PayPal',
            self::TYPE_STRIPE      => 'Stripe',
            self::TYPE_PAYONEER    => 'Payoneer',
            self::TYPE_2CHECKOUT   => '2Checkout',
        ];

    final public const int STATUS_INACTIVE = 0;

    final public const int STATUS_ACTIVE = 1;

    final public const array STATUSES
        = [
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_ACTIVE   => 'Active',
        ];

    final public const int IS_DEFAULT_NO = 0;

    final public const int IS_DEFAULT_YES = 1;

    final public const array IS_DEFAULTS
        = [
            self::IS_DEFAULT_NO  => 'No',
            self::IS_DEFAULT_YES => 'Yes',
        ];

}
