<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    10:48
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Security\Handler;

use App\Entity\User;
use App\Repository\UserRepository;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;

/**
 * Class AdminAuthenticationSuccessHandler.
 *
 * Handles successful authentication events for the admin module.
 * This class implements the AuthenticationSuccessHandlerInterface to provide custom
 * handling of successful logins, such as resetting rate limits and updating user timestamps.
 */
class AdminAuthenticationSuccessHandler implements AuthenticationSuccessHandlerInterface
{

    private RateLimiterFactory $adminLimiter;
    private UrlGeneratorInterface $urlGenerator;
    private UserRepository $userRepository;

    public function __construct(
        RateLimiterFactory $adminLimiter,
        UrlGeneratorInterface $urlGenerator,
        UserRepository $userRepository,
    ) {
        $this->adminLimiter = $adminLimiter;
        $this->urlGenerator = $urlGenerator;
        $this->userRepository = $userRepository;
    }

    /**
     * Handles a successful authentication event.
     *
     * @param Request $request The request object.
     * @param TokenInterface $token The authentication token.
     *
     * @return RedirectResponse A redirect response to the admin dashboard.
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token): RedirectResponse
    {
        $clientIp = $request->getClientIp();
        $limiter = $this->adminLimiter->create($clientIp);
        $limiter->reset();

        $user = $token->getUser();

        if ($user instanceof User) {
            $user->loadTimestamps()->setLastLoginAt(new DateTimeImmutable());

            if (!$this->userRepository->save($user)) {
                flash()->error('Failed to update user.');
            }
        }

        flash()->success('Login successful!');

        return new RedirectResponse($this->urlGenerator->generate('app_admin_dashboard_index'));
    }

}
