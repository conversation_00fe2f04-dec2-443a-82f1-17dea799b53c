<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\UserWallet;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserWalletMetaData.
 *
 * This class represents meta-data about a user-wallet.
 *
 * It is used as an embeddable entity in the UserWallet entity.
 */
#[ORM\Embeddable]
final class UserWalletMetaData
{

    #[ORM\Column(name: 'credit', type: 'decimal', precision: 65, scale: 2, options: ['default' => 0.00])]
    private string $credit = '0.00';

    public function getCredit(): float
    {
        return (float)($this->credit ?? '0.00');
    }

    public function setCredit(float $credit): UserWalletMetaData
    {
        $this->credit = number_format($credit, 2, '.', '');

        return $this;
    }

}
