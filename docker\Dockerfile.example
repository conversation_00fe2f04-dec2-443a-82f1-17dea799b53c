FROM php:8.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    zip \
    curl \
    sudo \
    unzip \
    libicu-dev \
    libbz2-dev \
    libpng-dev \
    libjpeg-dev \
    libmcrypt-dev \
    libreadline-dev \
    libfreetype6-dev \
    libxml2-dev \
    libzip-dev \
    libonig-dev \
    g++ \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy extension installer and install extensions in one layer
COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/bin/

# Install PHP extensions in one RUN to reduce layers
RUN install-php-extensions \
    bz2 \
    intl \
    bcmath \
    opcache \
    calendar \
    pdo \
    pdo_mysql \
    mysqli \
    zip \
    gd \
    exif \
    pcntl \
    xml \
    mbstring \
    redis

# Install Composer (using only one method)
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
RUN chmod +x /usr/bin/composer

RUN mkdir -p /var/www/.composer/cache && \
    chown -R www-data:www-data /var/www/.composer/cache && \
    chmod -R 775 /var/www/.composer/cache

# Create directory for app and required Symfony directories
RUN if [ ! -d /var/www/zenshop/var/cache ] || [ ! -d /var/www/zenshop/var/log ]; then \
    mkdir -p /var/www/zenshop/var/cache /var/www/zenshop/var/log; \
    fi

# Create and configure user
RUN useradd -G www-data,root -u 1000 -d /home/<USER>
    && mkdir -p /home/<USER>/.composer \
    && chown -R devuser:devuser /home/<USER>

# Set proper permissions
RUN chown -R www-data:www-data /var/www/zenshop

# Set working directory
WORKDIR /var/www/zenshop

# These ports are only for documentation, they don't actually publish ports
# Port publishing should be done in docker-compose.yml
EXPOSE 9000
