<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    6/7/2025
 * @time    3:34 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Service;

use App\Domain\Admin\Module\AccountManagement\Contract\CreateNewAccountServiceInterface;
use App\Domain\Admin\Module\AccountManagement\DTO\CreateNewAccountDTO;
use App\Entity\Client;
use App\Entity\Constant\UserConstant;
use App\Entity\User;
use App\Entity\UserProfile;
use App\Entity\UserWallet;
use App\Repository\ClientRepository;
use App\Repository\UserProfileRepository;
use App\Repository\UserRepository;
use App\Repository\UserWalletRepository;
use DateTimeImmutable;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * Class CreateNewAccountService
 *
 * Handles creation of a new user account and associated entities
 * like profile, client, and wallet based on the user's role.
 */
class CreateNewAccountService implements CreateNewAccountServiceInterface
{

    private UserRepository $userRepository;

    private UserProfileRepository $userProfileRepository;

    private ClientRepository $clientRepository;

    private UserWalletRepository $userWalletRepository;

    private Security $security;

    private UserPasswordHasherInterface $passwordHashed;

    public function __construct(
        UserRepository $userRepository,
        UserProfileRepository $userProfileRepository,
        ClientRepository $clientRepository,
        UserWalletRepository $userWalletRepository,
        Security $security,
        UserPasswordHasherInterface $passwordHashed,
    ) {
        $this->userRepository = $userRepository;
        $this->userProfileRepository = $userProfileRepository;
        $this->clientRepository = $clientRepository;
        $this->userWalletRepository = $userWalletRepository;
        $this->security = $security;
        $this->passwordHashed = $passwordHashed;
    }

    /**
     * Creates a new account along with role-specific entities.
     *
     * @param  CreateNewAccountDTO  $dto
     *
     * @return bool
     */
    public function createNewAccount(CreateNewAccountDTO $dto): bool
    {
        $user = new User($dto->email);

        $hashedPassword = $this->passwordHashed->hashPassword($user, $dto->password);
        $user->loadCredentials()->setPassword($hashedPassword)
            ->setRoles([$dto->role]);

        $user->loadMetaData()->setUsername($dto->userName)
            ->setStatus($dto->status)
            ->setRegistrationIp($dto->registrationIp);

        $user->onPrePersist();

        $currentUser = $this->security->getUser();
        if ($currentUser instanceof User) {
            $user->loadMetaData()->setParentId($currentUser->getId());
        }

        return $this->createUserProfileEntity($user, $dto) && $this->createAccountType($user, $dto);
    }

    /**
     * Creates and links a user profile to the account.
     *
     * @param  User  $user
     * @param  CreateNewAccountDTO  $dto
     *
     * @return bool
     */
    public function createUserProfileEntity(User $user, CreateNewAccountDTO $dto): bool
    {
        $userProfile = new UserProfile();
        $userProfile->setUser($user);
        $userProfile->loadMetaData()->setFirstName($dto->firstName)
            ->setLastName($dto->lastName);
        $user->setUserProfile($userProfile);

        return $this->userRepository->save($user) && $this->userProfileRepository->save($userProfile);
    }

    /**
     * Creates additional entities based on account role (e.g. Client, Reseller, Partner).
     *
     * @param  User  $user
     * @param  CreateNewAccountDTO  $dto
     *
     * @return bool
     */
    public function createAccountType(User $user, CreateNewAccountDTO $dto): bool
    {
        if ($dto->role === UserConstant::ROLE_CLIENT) {
            $client = new Client();
            $client->setUser($user);
            $client->loadTimestamps()
                ->setLastRenewAt(new DateTimeImmutable());
            $client->onPrePersist();
            $userWallet = new UserWallet();
            $userWallet->setUser($user);
            $userWallet->loadMetaData()->setCredit(0.00);
            $userWallet->onPrePersist();

            return $this->clientRepository->save($client) && $this->userWalletRepository->save($userWallet);
        }

        return true;
    }

    /**
     * Check unique for email.
     *
     * @param  string  $email
     *
     * @return bool
     */
    public function checkUniqueEmail(string $email): bool
    {
        $results = $this->userRepository->findWhere(['credentials.email' => $email]);

        return empty($results);
    }

}
