<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\Currency\CurrencyMetaData;
use App\Repository\CurrencyRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Currency.
 *
 * Represents a currency in the system. Maps to the `currencies` table in the database.
 */
#[ORM\Entity(repositoryClass: CurrencyRepository::class)]
#[ORM\Table(name: 'currencies')]
class Currency extends BaseEntity
{

    #[ORM\Embedded(class: CurrencyMetaData::class, columnPrefix: false)]
    private CurrencyMetaData $metaData;

    public function __construct(string $code)
    {
        $this->metaData = new CurrencyMetaData($code);
    }

    public function loadMetaData(): CurrencyMetaData
    {
        return $this->metaData;
    }

}
