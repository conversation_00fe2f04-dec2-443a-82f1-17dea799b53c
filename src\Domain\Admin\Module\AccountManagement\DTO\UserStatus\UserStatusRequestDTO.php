<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/18/2025
 * @time 6:53 PM
 */

namespace App\Domain\Admin\Module\AccountManagement\DTO\UserStatus;

/**
 * Class UserStatusRequestDTO
 *
 * Data Transfer Object for carrying the user ID related to a status update or query request.
 */
class UserStatusRequestDTO
{

    /**
     * The ID of the user whose status is to be updated or retrieved.
     *
     * @var int
     */
    public int $userId;

}
