<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Client;

use App\Entity\Constant\ClientConstant;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class ClientMetaData.
 *
 * This class represents meta-data about a client.
 *
 * It is used as an embeddable entity in the Client entity.
 */
#[ORM\Embeddable]
final class ClientMetaData
{

    #[ORM\Column(name: 'is_auto_renew', type: 'smallint', options: ['default' => ClientConstant::AUTO_RENEW_ENABLE])]
    private int $isAutoRenew = ClientConstant::AUTO_RENEW_ENABLE;

    public function getIsAutoRenew(): int
    {
        return $this->isAutoRenew;
    }

    public function setIsAutoRenew(int $isAutoRenew = ClientConstant::AUTO_RENEW_ENABLE): ClientMetaData
    {
        $this->isAutoRenew = $isAutoRenew;

        return $this;
    }

}
