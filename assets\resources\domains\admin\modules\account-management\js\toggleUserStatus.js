function toggleUserStatus (button) {
    const userId = button.getAttribute('data-user-id')
    const action = button.getAttribute('data-action')

    fetch(`/admin/manage/account/list/${action}/${userId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    }).then(res => res.json()).then(data => {
        if (data.success) {
            location.reload()
        } else {
            location.reload()
        }
    }).catch(error => {
        console.error(error)
    })
}
