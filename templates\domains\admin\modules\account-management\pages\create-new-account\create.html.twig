{% extends 'domains/admin/shared/layouts/main.html.twig' %}

{% block title %}Create New Account{% endblock %}

{% block content %}
    <div class="col-span-12">
        <div class="flex items-center space-x-4 py-5 lg:py-6">
            <h2 class="text-xl font-medium text-slate-800 dark:text-navy-50">
                Create New Account
            </h2>
            <div class="hidden h-full py-1 sm:flex">
                <div class="h-full w-px bg-slate-300 dark:bg-navy-600"></div>
            </div>
            {{ breadcrumb(
                [
                    {'name': 'Account Management', 'url': path('app_admin_account_list_index'), 'sidebar': 'management'},
                    {'name': 'Create New Account', 'url': path('app_admin_account_management_create'), 'sidebar': 'management'}
                ]
            ) }}
        </div>

        {{ form_start(form, {'attr': {'id': 'create-account-form', 'class': 'space-y-4', 'data-turbo': 'true'}}) }}
        <div class="card">
            <div
                class="flex flex-col items-center space-y-4 border-b border-slate-200 p-4 dark:border-navy-500 sm:flex-row sm:justify-between sm:space-y-0 sm:px-5">
                <h2 class="text-lg font-medium tracking-wide text-slate-700 dark:text-navy-100">
                    Account Information
                </h2>
            </div>
            <div class="p-4 sm:p-5">
                {% for label, messages in app.flashes %}
                    {% for message in messages %}
                        <div class="alert {{ label == 'success' ? 'bg-success' : 'bg-error' }} mb-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <i class="fa {{ label == 'success' ? 'fa-check' : 'fa-exclamation-triangle' }} text-white"></i>
                                    <p class="text-white">{{ message | raw }}</p>
                                </div>
                                <button
                                    class="btn h-7 w-7 rounded-full p-0 text-white hover:bg-white/20 focus:bg-white/20 active:bg-white/25">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% endfor %}

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div class="grid grid-cols-1 gap-4">
                        <label class="block" for="{{ form.firstName.vars.id }}">
                            <span>{{ form_label(form.firstName) }}</span>
                            {{ form_widget(form.firstName, {'attr': {'class': 'form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                        </label>
                    </div>
                    <div class="grid grid-cols-1 gap-4">
                        <label class="block" for="{{ form.lastName.vars.id }}">
                            <span>{{ form_label(form.lastName) }}</span>
                            {{ form_widget(form.lastName, {'attr': {'class': 'form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                        </label>
                    </div>
                </div>

                <div class="my-4 h-px bg-slate-200 dark:bg-navy-500"></div>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <label class="block" for="{{ form.userName.vars.id }}">
                        <span>{{ form_label(form.userName) }}</span>
                        {{ form_widget(form.userName, {'attr': {'class': 'form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                    </label>
                    <label class="block" for="{{ form.email.vars.id }}">
                        <span>{{ form_label(form.email) }}</span>
                        {{ form_widget(form.email, {'attr': {'class': 'form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                    </label>
                </div>

                <div class="my-4 h-px bg-slate-200 dark:bg-navy-500"></div>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <label class="block" for="{{ form.password.vars.id }}">
                        <span>{{ form_label(form.password) }}</span>
                        {{ form_widget(form.password, {'attr': {'class': 'form-input w-full rounded-lg border border-slate-300 bg-transparent px-3 py-2 placeholder:text-slate-400/70 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                    </label>
                    <label class="block" for="registration_ip">
                        <span>Registration IP</span>
                        <input type="text" id="registration_ip" value="{{ registration_ip }}" readonly
                               class="form-input w-full rounded-lg border border-slate-300 bg-slate-150 px-3 py-2 text-slate-500 dark:border-navy-450 dark:bg-navy-700 dark:text-navy-300"/>
                    </label>
                </div>

                <div class="my-4 h-px bg-slate-200 dark:bg-navy-500"></div>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <label class="block" for="{{ form.role.vars.id }}">
                        <span>{{ form_label(form.role) }}</span>
                        {{ form_widget(form.role, {'attr': {'class': 'form-select w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                    </label>
                    <label class="block" for="{{ form.status.vars.id }}">
                        <span>{{ form_label(form.status) }}</span>
                        {{ form_widget(form.status, {'attr': {'class': 'form-select w-full rounded-lg border border-slate-300 bg-white px-3 py-2 hover:border-slate-400 focus:border-primary dark:border-navy-450 dark:bg-navy-700 dark:hover:border-navy-400 dark:focus:border-accent'}}) }}
                    </label>
                </div>

                <div class="flex justify-center space-x-2 pt-4">
                    <button type="button" @click="window.location.href='{{ path('app_admin_account_list_index') }}'"
                            class="btn min-w-[7rem] rounded-full border border-slate-300 font-medium text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                        Cancel
                    </button>
                    <button type="submit"
                            class="btn min-w-[7rem] rounded-full bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">
                        Save
                    </button>
                </div>
            </div>
        </div>
        {{ form_end(form) }}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        (function () {
            function preventSpace (e) {
                if (e.key === ' ' || e.keyCode === 32) {
                    e.preventDefault()
                }
            }

            function removeSpaces (input) {
                if (input?.value?.includes(' ')) {
                    input.value = input.value.replace(/\s/g, '')
                }
            }

            function handlePaste (e) {
                e.preventDefault()
                this.value = (e.clipboardData || window.clipboardData).getData('text').replace(/\s/g, '')
            }

            function setupNoSpaceInputs () {
                const inputs = [
                    document.getElementById('{{ form.userName.vars.id }}'),
                    document.getElementById('{{ form.email.vars.id }}'),
                ]

                inputs.forEach(input => {
                    if (!input) {
                        return
                    }

                    removeSpaces(input)

                    input.removeEventListener('keydown', preventSpace)
                    input.removeEventListener('input', () => removeSpaces(input))
                    input.removeEventListener('paste', handlePaste)

                    input.addEventListener('keydown', preventSpace)
                    input.addEventListener('input', () => removeSpaces(input))
                    input.addEventListener('paste', handlePaste)
                })
            }

            setupNoSpaceInputs()
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', setupNoSpaceInputs)
            }
            [0, 100, 500].forEach(ms => setTimeout(setupNoSpaceInputs, ms))
            window.addEventListener('load', setupNoSpaceInputs)

            new MutationObserver(setupNoSpaceInputs).observe(document.body, {
                childList: true,
                subtree: true,
            })

            const form = document.getElementById('create-account-form')
            form?.addEventListener('submit', () => {
                [
                    document.getElementById('{{ form.userName.vars.id }}'),
                    document.getElementById('{{ form.email.vars.id }}'),
                ].forEach(input => input && removeSpaces(input))
            })
        })()
    </script>
{% endblock %}
