<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Client;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class ClientTimestamps.
 *
 * This class represents the timestamps related to a client.
 *
 * It is used as an embeddable entity in the Client entity.
 */
#[ORM\Embeddable]
final class ClientTimestamps
{

    #[ORM\Column(name: 'last_renew_at', type: 'datetime_immutable')]
    private DateTimeImmutable $lastRenewAt;

    public function getLastRenewAt(): DateTimeImmutable
    {
        return $this->lastRenewAt;
    }

    public function setLastRenewAt(DateTimeImmutable $lastRenewAt): ClientTimestamps
    {
        $this->lastRenewAt = $lastRenewAt;

        return $this;
    }

}
