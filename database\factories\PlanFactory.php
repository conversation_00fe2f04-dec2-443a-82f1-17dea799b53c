<?php

declare(strict_types=1);

namespace Database\Factory;

use App\Entity\Plan;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

/**
 * Class PlanFactory.
 */
class PlanFactory extends Fixture
{

    public function load(ObjectManager $manager): void
    {
        $this->addTrialPlan($manager);
    }

    private function addTrialPlan(ObjectManager $manager): void
    {
        $plan = new Plan('trial');

        $plan->loadMetadata()->setName('Trial Plan');
        $plan->loadMetadata()->setDescription('The trial plan is intended for exploring and experiencing the system.');
        $plan->loadMetadata()->setType(0);
        $plan->loadMetaData()->setLength('7 days');

        $manager->persist($plan);
        $manager->flush();
    }

}
