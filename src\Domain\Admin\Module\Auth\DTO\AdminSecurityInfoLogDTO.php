<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    18:50
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\DTO;

/**
 * Class AdminSecurityInfoLogDTO.
 *
 * This Data Transfer Object (DTO) is used to encapsulate the data required for writing an info log
 * related to admin security events. It contains the message, context, and extra information that
 * will be logged.
 */
final class AdminSecurityInfoLogDTO
{

    public string $message;
    public ?array $context = null;
    public ?array $extra = null;

}
