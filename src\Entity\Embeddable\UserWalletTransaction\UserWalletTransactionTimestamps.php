<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\UserWalletTransaction;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserWalletTransactionTimestamps.
 *
 * This class represents the timestamps related to a user-wallet-transaction.
 *
 * It is used as an embeddable entity in the UserWalletTransaction entity.
 */
#[ORM\Embeddable]
final class UserWalletTransactionTimestamps
{

    #[ORM\Column(name: 'logged_at', type: 'datetime_immutable')]
    private DateTimeImmutable $loggedAt;

    public function getLoggedAt(): DateTimeImmutable
    {
        return $this->loggedAt;
    }

    public function setLoggedAt(DateTimeImmutable $loggedAt): UserWalletTransactionTimestamps
    {
        $this->loggedAt = $loggedAt;

        return $this;
    }

}
