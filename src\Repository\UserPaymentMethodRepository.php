<?php

declare(strict_types=1);

namespace App\Repository;

use App\Base\BaseRepository;
use App\Entity\UserPaymentMethod;

/**
 * Class UserPaymentMethodRepository.
 *
 * This class is responsible for handling database operations related to user payment methods.
 * It extends the base repository class and can be customized with specific methods as needed.
 */
class UserPaymentMethodRepository extends BaseRepository
{

    protected function loadEntity(): string
    {
        return UserPaymentMethod::class;
    }

}
