<?php

declare(strict_types=1);

namespace App\Utils\Helper;

use Random\RandomException;

/**
 * Class NumberHelper.
 *
 * A utility class that provides helper methods for number-related operations.
 */
class NumberHelper
{

    /**
     * Generates a random numeric string of a specified length.
     *
     * @param  int  $length  The length of the random string to generate. Default is 6.
     *
     * @return string A random numeric string of the specified length.
     *
     * @throws RandomException
     */
    public static function random(int $length = 6): string
    {
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }

        return $randomString;
    }

}
