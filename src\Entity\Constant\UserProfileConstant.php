<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class UserProfileConstant.
 *
 * This class holds all constant values related to the UserProfile entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing user-profile-specific data.
 */
final class UserProfileConstant
{

    final public const int GENDER_MALE = 0;

    final public const int GENDER_FEMALE = 1;

    final public const int GENDER_OTHER = 2;

    final public const array GENDERS
        = [
            self::GENDER_MALE   => 'Male',
            self::GENDER_FEMALE => 'Female',
            self::GENDER_OTHER  => 'Other',
        ];

}
