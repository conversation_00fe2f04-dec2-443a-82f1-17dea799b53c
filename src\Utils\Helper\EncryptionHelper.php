<?php

declare(strict_types=1);

namespace App\Utils\Helper;

use Random\RandomException;

/**
 * Class EncryptionHelper.
 *
 * This class provides the ability to encrypt and decrypt data.
 */
class EncryptionHelper
{

    private const string CIPHER = 'aes-256-cbc';

    private const string PASSPHRASE = 'EZMate';

    /**
     * Encrypt the given data using AES-256-CBC encryption.
     *
     * @param  string  $data
     *
     * @return false|string
     *
     * @throws RandomException
     */
    public static function encrypt(string $data): false|string
    {
        $length = openssl_cipher_iv_length('aes-256-cbc');
        $iv = random_bytes($length);

        while (false === $iv) {
            try {
                $iv = random_bytes($length);
            } catch (RandomException $e) {
                echo 'Error: '.$e->getMessage();

                sleep(1);
            }
        }

        $key = openssl_encrypt($data, self::CIPHER, self::PASSPHRASE, 0, $iv);

        return base64_encode($iv.$key);
    }

    /**
     * Return decrypted data.
     *
     * @param  string  $encryptedData  Encrypted data to decrypt.
     *
     * @return false|string Decrypted data or false on failure.
     */
    public static function decrypt(string $encryptedData): false|string
    {
        $encryptedData = base64_decode($encryptedData);

        $length = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($encryptedData, 0, $length);
        $cipherText = substr($encryptedData, $length);

        return openssl_decrypt($cipherText, self::CIPHER, self::PASSPHRASE, 0, $iv);
    }

}

