<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    10:46
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Security\Handler;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAccountStatusException;
use Symfony\Component\Security\Http\Authentication\AuthenticationFailureHandlerInterface;

/**
 * Class AdminAuthenticationFailureHandler.
 *
 * Handles authentication failures for the admin module.
 * This class implements the AuthenticationFailureHandlerInterface to provide custom
 * handling of authentication failures, such as rate limiting and error messages.
 */
class AdminAuthenticationFailureHandler implements AuthenticationFailureHandlerInterface
{

    private RateLimiterFactory $adminLimiter;
    private UrlGeneratorInterface $urlGenerator;

    public function __construct(
        RateLimiterFactory $adminLimiter,
        UrlGeneratorInterface $urlGenerator
    ) {
        $this->adminLimiter = $adminLimiter;
        $this->urlGenerator = $urlGenerator;
    }

    /**
     * Handles the authentication failure response.
     *
     * @param Request $request The HTTP request object.
     * @param AuthenticationException $exception The authentication exception that occurred.
     *
     * @return RedirectResponse A redirect response to the login page with an error message.
     */
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): RedirectResponse
    {
        $clientIp = $request->getClientIp();
        $limiter = $this->adminLimiter->create($clientIp);

        if ($exception instanceof CustomUserMessageAccountStatusException) {
            flash()->error($exception->getMessage());

            return new RedirectResponse($this->urlGenerator->generate('app_admin_auth_login'));
        }

        $rateLimit = $limiter->consume();

        if (!$rateLimit->isAccepted()) {
            flash()->error(
                "Your account has been temporarily locked due to too many failed login attempts. Please try again after 15 minutes."
            );

            return new RedirectResponse($this->urlGenerator->generate('app_admin_auth_login'));
        }

        flash()->error("Incorrect email or password. Please try again.");

        return new RedirectResponse($this->urlGenerator->generate('app_admin_auth_login'));
    }

}
