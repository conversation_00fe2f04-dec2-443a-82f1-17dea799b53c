<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email  <EMAIL>
 * @date    6/18/2025
 * @time    7:15 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Class ListAccountDTO.
 *
 * This class represents the data transfer object (DTO) used to return a list of user accounts.
 * It holds an array of accounts, each containing information such as username, email, status, etc.
 * This DTO is typically used to transfer account data between services and layers in the application.
 *
 */
final class ListAccountDTO
{

    /**
     * @var array An array of user accounts
     */
    public array $accounts = [];

}
