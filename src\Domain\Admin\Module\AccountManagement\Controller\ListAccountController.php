<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email  <EMAIL>
 * @date    6/18/2025
 * @time    7:11 PM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Domain\Admin\Module\AccountManagement\Contract\ListAccountServiceInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ListAccountController
 *
 * Controller for managing user accounts in the admin panel.
 *
 * Provides endpoints for viewing account lists, updating account statuses,
 * performing bulk actions, and handling account lifecycle transitions.
 */
#[Route('/admin/manage/account/list', name: 'app_admin_account_list_')]
class ListAccountController extends AdminController
{

    public function __construct(
        private readonly ListAccountServiceInterface $accountManagementService
    ) {
        parent::__construct();
    }

    /**
     * Action Index.
     *
     * Displays the main account management interface.
     */
    #[Route(name: 'index', methods: ['GET'])]
    public function index(): Response
    {
        return $this->view('account-management/pages/list-account/list-account-tabulator');
    }

    /**
     * Action Get List.
     *
     * Returns a JSON list of all user accounts for display purposes.
     */
    #[Route('/getList', name: 'getList', methods: ['GET'])]
    public function getList(): JsonResponse
    {
        return $this->json($this->accountManagementService->getList()->accounts);
    }

}
