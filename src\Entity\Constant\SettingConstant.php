<?php

declare(strict_types=1);

namespace App\Entity\Constant;

/**
 * Class SettingConstant.
 *
 * This class holds all constant values related to the Setting entity.
 * These constants are meant to be used throughout the application to ensure consistency
 * when referencing setting-specific data.
 */
final class SettingConstant
{

    final public const int STATUS_INACTIVE = 0;

    final public const int STATUS_ACTIVE = 1;

    final public const array TYPES
        = [
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_ACTIVE   => 'Active',
        ];

}
