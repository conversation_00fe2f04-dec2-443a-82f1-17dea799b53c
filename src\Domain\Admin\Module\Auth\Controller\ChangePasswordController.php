<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    12:10
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Controller;

use App\Domain\Admin\Base\AdminController;
use App\Domain\Admin\Module\Auth\Contract\ChangePasswordServiceInterface;
use App\Domain\Admin\Module\Auth\Contract\SessionManagerServiceInterface;
use App\Domain\Admin\Module\Auth\Contract\WriteInfoLogServiceInterface;
use App\Domain\Admin\Module\Auth\DTO\AdminAccountPasswordRequestDTO;
use App\Domain\Admin\Module\Auth\DTO\AdminSecurityInfoLogDTO;
use App\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\RateLimiter\LimiterInterface;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class ChangePasswordController.
 *
 * This controller handles the password-changing functionality for admin users.
 * It provides a form for changing passwords and processes the password change requests.
 */
#[Route('/admin', name: 'app_admin_auth_')]
class ChangePasswordController extends AdminController
{

    // Define constants for routes and rate limit thresholds.
    private const string DASHBOARD_ROUTE = 'app_admin_dashboard_index';
    // Define the route for the change password page.
    private const string CHANGE_PASSWORD_ROUTE = 'app_admin_auth_change_password';
    // Define the rate limit threshold for password changes.
    private const int RATE_LIMIT_THRESHOLD = 0;

    private ChangePasswordServiceInterface $changePasswordService;
    private ValidatorInterface $validator;
    private RateLimiterFactory $passwordChangeLimiter;
    private SessionManagerServiceInterface $sessionManager;
    private WriteInfoLogServiceInterface $writeInfoLogService;

    public function __construct(
        ChangePasswordServiceInterface $changePasswordService,
        ValidatorInterface $validator,
        RateLimiterFactory $passwordChangeLimiter,
        SessionManagerServiceInterface $sessionManager,
        WriteInfoLogServiceInterface $writeInfoLogService
    ) {
        parent::__construct();
        $this->changePasswordService = $changePasswordService;
        $this->validator = $validator;
        $this->passwordChangeLimiter = $passwordChangeLimiter;
        $this->sessionManager = $sessionManager;
        $this->writeInfoLogService = $writeInfoLogService;
    }

    /**
     * Display the password change form and handle the password change request.
     *
     * @param $user . The currently authenticated user.
     * @param Request $request The HTTP request object containing form data.
     *
     * @return Response Rendered response for the change password page or redirection.
     */
    #[Route('/change-password', name: 'change_password', methods: ['GET', 'POST'])]
    public function index(#[CurrentUser] $user, Request $request): Response
    {
        $limiter = $this->getRateLimiter($user->getId());

        // Check if the user is rate-limited and redirect immediately
        if ($this->isUserRateLimited($limiter)) {
            $this->notifyRateLimited();

            return $this->redirectToRoute(self::DASHBOARD_ROUTE);
        }

        // Handle GET request - just show the form
        if (!$request->isMethod('POST')) {
            return $this->renderChangePasswordForm();
        }

        // Handle POST request - process form submission
        $routeName = $this->handlePasswordChangeRequest($user, $request, $limiter);

        return $this->redirectToRoute($routeName);
    }

    /**
     * Handle password change request processing with reduced cognitive complexity.
     *
     * @param User $user The currently authenticated user.
     * @param Request $request The HTTP request object containing form data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return string The route to redirect to after processing the request.
     */
    private function handlePasswordChangeRequest(User $user, Request $request, LimiterInterface $limiter): string
    {
        // Initial check for rate limiting
        if ($this->checkRateLimitAndNotify($limiter)) {
            return self::DASHBOARD_ROUTE;
        }

        // Create DTO from form data
        $dto = $this->createPasswordRequestDTO($request);

        // Process the request through a sequence of steps
        return $this->processPasswordChangeSteps($user, $dto, $limiter, $request);
    }

    /**
     * Process password change through a series of validation steps.
     *
     * @param User $user The currently authenticated user.
     * @param AdminAccountPasswordRequestDTO $dto The data transfer object containing password request data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     * @param Request $request The HTTP request object containing form data.
     *
     * @return string The route to redirect to after processing the request.
     */
    private function processPasswordChangeSteps(
        User $user,
        AdminAccountPasswordRequestDTO $dto,
        LimiterInterface $limiter,
        Request $request
    ): string {
        // The default route is the change password form
        $resultRoute = self::CHANGE_PASSWORD_ROUTE;

        // Step 1: Validate input and check rate limits
        $validationResult = $this->validateAndCheckRateLimit($dto, $limiter);

        if ($validationResult === true) {
            // Step 2: Verify password and perform change if valid
            $resultRoute = $this->verifyAndProcessPasswordChange($user, $dto, $limiter, $request);
        } elseif ($validationResult === 'rate_limited') {
            $resultRoute = self::DASHBOARD_ROUTE;
        }

        return $resultRoute;
    }

    /**
     * Validate from input and check rate limits.
     *
     * @param AdminAccountPasswordRequestDTO $dto The data transfer object containing password request data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return true|string true if validation passes, 'rate_limited' if rate limited, or false if validation fails
     */
    private function validateAndCheckRateLimit(
        AdminAccountPasswordRequestDTO $dto,
        LimiterInterface $limiter
    ): bool|string {
        // Step 1: Validate form input
        if (!$this->validatePasswordRequest($dto)) {
            return false;
        }

        // Step 2: Check rate limit again
        if ($this->checkRateLimitAndNotify($limiter)) {
            return 'rate_limited';
        }

        return true;
    }

    /**
     * Verify the current password and process change if valid.
     *
     * @param User $user The currently authenticated user.
     * @param AdminAccountPasswordRequestDTO $dto The data transfer object containing password request data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     * @param Request $request The HTTP request object containing form data.
     *
     * @return string The route to redirect to after processing the request.
     */
    private function verifyAndProcessPasswordChange(
        User $user,
        AdminAccountPasswordRequestDTO $dto,
        LimiterInterface $limiter,
        Request $request
    ): string {
        // Attempt to verify and change password
        $passwordChangeResult = $this->attemptPasswordChange($user, $dto, $limiter);

        // If rate-limited during verification or change, return to dashboard
        if ($passwordChangeResult === 'rate_limited') {
            return self::DASHBOARD_ROUTE;
        }

        // If password verification failed but not rate-limited yet
        if ($passwordChangeResult === false) {
            return self::CHANGE_PASSWORD_ROUTE;
        }

        // Password change successful, complete the process
        $this->completePasswordChange($user, $request, $limiter);
        return self::DASHBOARD_ROUTE;
    }

    /**
     * Attempt to verify and change the password.
     *
     * @param User $user The currently authenticated user.
     * @param AdminAccountPasswordRequestDTO $dto The data transfer object containing password request data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return bool|string true if successful, false if failed but not rate limited, 'rate_limited' if rate limited.
     */
    private function attemptPasswordChange(
        User $user,
        AdminAccountPasswordRequestDTO $dto,
        LimiterInterface $limiter
    ): bool|string {
        // First return: Check rate limit before attempting password change
        if ($this->checkRateLimitAndNotify($limiter)) {
            return 'rate_limited';
        }

        // Verify the current password and change to the new password in a single service call
        $passwordChangeSuccessful = $this->changePasswordService->verifyAndChangePassword(
            $user,
            $dto->currentPassword,
            $dto->newPassword
        );

        // Second return: If the password change is successful
        if ($passwordChangeSuccessful) {
            return true;
        }

        // Handle password verification or change failure
        // Consume a token on failure
        $limiter->consume();

        // Get remaining attempts after consumption
        $remainingAttempts = $this->getRemainingAttempts($limiter);

        // Display the appropriate error message based on remaining attempts
        if ($remainingAttempts > 0) {
            flash()->error(
                'Current password is incorrect. You have '.$remainingAttempts.' attempts remaining today.'
            );
        } else {
            // Display notification when becoming rate-limited
            $this->notifyRateLimited();
        }

        // Third return: Return appropriate status based on remaining attempts
        return $remainingAttempts > 0 ? false : 'rate_limited';
    }

    /**
     * Check rate limit and notify if limited.
     *
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return bool true if the user is rate-limited, false otherwise.
     */
    private function checkRateLimitAndNotify(LimiterInterface $limiter): bool
    {
        if ($this->isUserRateLimited($limiter)) {
            $this->notifyRateLimited();

            return true;
        }

        return false;
    }

    /**
     * Create password request DTO from request.
     *
     * @param Request $request The HTTP request object containing form data.
     *
     * @return AdminAccountPasswordRequestDTO The data transfer object containing password request data.
     */
    private function createPasswordRequestDTO(Request $request): AdminAccountPasswordRequestDTO
    {
        $requestData = new AdminAccountPasswordRequestDTO();

        $requestData->currentPassword = $request->request->get('current_password', '');
        $requestData->newPassword = $request->request->get('new_password', '');
        $requestData->confirmPassword = $request->request->get('confirm_new_password', '');

        return $requestData;
    }

    /**
     * Validate the password request DTO
     *
     * @param AdminAccountPasswordRequestDTO $dto The data transfer object containing password request data.
     *
     * @return bool true if validation passes, false otherwise.
     */
    private function validatePasswordRequest(AdminAccountPasswordRequestDTO $dto): bool
    {
        $errors = $this->validator->validate($dto);
        if (count($errors) === 0) {
            return true;
        }

        $errorMessages = [];
        foreach ($errors as $error) {
            $errorMessages[] = $error->getMessage();
        }

        flash()->error(implode('. ', $errorMessages));

        return false;
    }

    /**
     * Complete the password change process.
     *
     * @param User $user The currently authenticated user.
     * @param Request $request The HTTP request object containing form data.
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return void
     */
    private function completePasswordChange(User $user, Request $request, LimiterInterface $limiter): void
    {
        // Reset rate limiter
        $limiter->reset();

        // Invalidate other sessions
        $currentSessionId = $request->getSession()->getId();
        $this->sessionManager->invalidateOtherSessions($user->getId(), $currentSessionId);

        // Prepare log information
        $logInfo = new AdminSecurityInfoLogDTO();

        $logInfo->message = "User with ID {$user->getId()} changed their password successfully.";
        $logInfo->context = [
            'user_id'    => $user->getId(),
            'session_id' => $currentSessionId,
        ];
        $logInfo->extra = [
            'ip_address' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
        ];

        // Log the password change
        $logSuccess = $this->writeInfoLogService->write($logInfo);

        // Show the appropriate message
        if ($logSuccess) {
            flash()->success(
                'Your password has been changed successfully. All other sessions have been invalidated.'
            );
        } else {
            flash()->error('Password has been changed, but failed to log the action.');
        }
    }

    /**
     * Render the change-password form.
     *
     * @return Response Rendered response for the change password form.
     */
    private function renderChangePasswordForm(): Response
    {
        return $this->view('/auth/pages/change-password', [
            'form_message'     => 'Changing Password',
            'form_description' => 'Please fill in fields to change password.',
        ]);
    }

    /**
     * Get rate limiter for user ID.
     *
     * @param int $userId The ID of the user for whom the rate limiter is being created.
     *
     * @return LimiterInterface The rate limiter instance for the user.
     */
    private function getRateLimiter(int $userId): LimiterInterface
    {
        return $this->passwordChangeLimiter->create((string)$userId);
    }

    /**
     * Get remaining attempts from limiter.
     *
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return int The number of remaining attempts allowed for the user.
     */
    private function getRemainingAttempts(LimiterInterface $limiter): int
    {
        return $limiter->consume(0)->getRemainingTokens();
    }

    /**
     * Check if the user is rate-limited by counting remaining tokens.
     * User is considered rate-limited if remaining tokens are at or below a threshold.
     *
     * @param LimiterInterface $limiter The rate limiter for the user.
     *
     * @return bool true if the user is rate-limited, false otherwise.
     */
    private function isUserRateLimited(LimiterInterface $limiter): bool
    {
        return $this->getRemainingAttempts($limiter) <= self::RATE_LIMIT_THRESHOLD;
    }

    /**
     * Notify user they are rate-limited.
     *
     * @return void.
     */
    private function notifyRateLimited(): void
    {
        flash()->error(
            'You have exceeded the maximum number of password change attempts for today. Please try again tomorrow.'
        );
    }

}
