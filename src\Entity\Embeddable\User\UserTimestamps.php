<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\User;

use App\Entity\Trait\HasSoftDeleteableTrait;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class UserTimestamps.
 *
 * This class represents the timestamps related to a user.
 *
 * It is used as an embeddable entity in the User entity.
 */
#[ORM\Embeddable]
final class UserTimestamps
{

    use HasSoftDeleteableTrait;

    #[ORM\Column(name: 'last_login_at', type: 'datetime_immutable', nullable: true)]
    private ?DateTimeImmutable $lastLoginAt = null;

    #[ORM\Column(name: 'blocked_at', type: 'datetime_immutable', nullable: true)]
    private ?DateTimeImmutable $blockedAt = null;

    public function getLastLoginAt(): ?DateTimeImmutable
    {
        return $this->lastLoginAt;
    }

    public function setLastLoginAt(?DateTimeImmutable $lastLoginAt): UserTimestamps
    {
        $this->lastLoginAt = $lastLoginAt;

        return $this;
    }

    public function getBlockedAt(): ?DateTimeImmutable
    {
        return $this->blockedAt;
    }

    public function setBlockedAt(?DateTimeImmutable $blockedAt): UserTimestamps
    {
        $this->blockedAt = $blockedAt;

        return $this;
    }

}
