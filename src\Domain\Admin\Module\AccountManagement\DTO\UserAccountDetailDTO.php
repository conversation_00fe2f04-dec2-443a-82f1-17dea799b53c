<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    16:02
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Data Transfer Object (DTO) for comprehensive user account details.
 *
 * This DTO aggregates all pertinent information related to a specific user's
 * account, including general account data, profile details, type-specific information,
 * wallet data, transaction history, and their most recent purchase. It serves as a
 * holistic view for administrative purposes or detailed user profile displays.
 */
final class UserAccountDetailDTO
{
    public ?AccountInformationDTO $accountInformationDTO = null;
    public ?UserProfileDTO $userProfileDTO = null;
    public ?object $userTypeSpecificData = null;
    public ?UserWalletDTO $userWalletDTO = null;
    public ?array $userWalletTransactionsDTO = null;
    public ?object $lastItemPurchasedDTO = null;
}
