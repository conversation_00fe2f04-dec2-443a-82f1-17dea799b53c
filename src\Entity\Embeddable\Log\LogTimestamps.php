<?php

declare(strict_types=1);

namespace App\Entity\Embeddable\Log;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class LogTimestamps.
 *
 * This class represents a timestamp for logging purposes.
 * It is an embeddable entity that can be used in other entities to track when an event was logged.
 */
#[ORM\Embeddable]
class LogTimestamps
{

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private ?DateTimeImmutable $loggedAt = null;

    public function getLoggedAt(): ?DateTimeImmutable
    {
        return $this->loggedAt;
    }

    public function setLoggedAt(?DateTimeImmutable $loggedAt): LogTimestamps
    {
        $this->loggedAt = $loggedAt;

        return $this;
    }

}
