<?php

declare(strict_types=1);

namespace Database\Factory;

use App\Entity\Client;
use App\Entity\Constant\UserConstant;
use App\Entity\Constant\UserProfileConstant;
use App\Entity\Constant\UserWalletTransactionConstant;
use App\Entity\CreditPackage;
use App\Entity\User;
use App\Entity\UserProfile;
use App\Entity\UserWallet;
use App\Entity\UserWalletTransaction;
use DateTimeImmutable;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * Class UserFactory.
 */
class UserFactory extends Fixture
{

    private CreditPackage $creditPackage;

    public function __construct(private readonly UserPasswordHasherInterface $hasher)
    {
    }

    public function load(ObjectManager $manager): void
    {
        $this->creditPackage = $this->makeCreditPackage();

        $manager->persist($this->creditPackage);
        $manager->flush();

        $this->addMasterAccount($manager);
        $this->addAdminAccount($manager);
        $this->addSubAdminAccount($manager);
        $this->addUser($manager);
    }

    private function addMasterAccount(ObjectManager $manager): void
    {
        $user = new User('<EMAIL>');

        $user->loadCredentials()->setPassword($this->hasher->hashPassword($user, 'zenshop@2025!'));
        $user->loadCredentials()->setRoles(['ROLE_ADMIN']);

        $user->loadMetaData()->setUsername('Zen Master');
        $user->loadMetadata()->setStatus(1);

        $manager->persist($user);
        $manager->flush();
    }

    private function addAdminAccount(ObjectManager $manager): void
    {
        $user = new User('<EMAIL>');

        $user->loadCredentials()->setPassword($this->hasher->hashPassword($user, 'zenshop@2025!'));
        $user->loadCredentials()->setRoles(['ROLE_ADMIN']);

        $user->loadMetaData()->setUsername('Zen Admin');
        $user->loadMetadata()->setStatus(1);

        $manager->persist($user);
        $manager->flush();
    }

    private function addSubAdminAccount(ObjectManager $manager): void
    {
        $admin = $manager->getRepository(User::class)->findOneBy(['credentials.email' => '<EMAIL>']);

        if ($admin) {
            $user = new User('<EMAIL>');

            $user->loadCredentials()->setPassword($this->hasher->hashPassword($user, 'zenshop@2025!'));
            $user->loadCredentials()->setRoles(['ROLE_ADMIN']);

            $user->loadMetaData()->setParentId($admin->getId());
            $user->loadMetaData()->setUsername('Zen Manager');
            $user->loadMetadata()->setStatus(1);

            $manager->persist($user);
            $manager->flush();
        }
    }

    private function addUser(ObjectManager $manager): void
    {
        $users = [];
        for ($i = 0; $i < 10; $i++) {
            $user = new User('user'.$i.'@example.com');

            $user->loadCredentials()->setPassword($this->hasher->hashPassword($user, '1'));
            $user->loadMetaData()->setUsername('user'.$i);

            if ($i % 2 === 0) {
                $user->loadMetaData()->setParentId(null);
                $user->loadCredentials()->setRoles([UserConstant::ROLE_CLIENT]);
                $user->loadMetaData()->setStatus(UserConstant::STATUS_ACTIVE);
            } else {
                $user->loadMetaData()->setParentId($users[$i - 1]->getId());
                $user->loadCredentials()->setRoles([UserConstant::ROLE_USER]);
                $user->loadMetaData()->setStatus(UserConstant::STATUS_INACTIVE);
            }

            $user->loadMetaData()->setRegistrationIp('127.0.0.1');
            $manager->persist($user);
            $users[] = $user;

            $manager->flush();

            $manager->persist($this->makeProfile($user));

            if ($i % 2 === 0) {
                $manager->persist($this->makeClient($user));

                $userWallet = $this->makeUserWallet($user);
                $manager->persist($userWallet);

                $transactions = $this->makeUserWalletTransactions($userWallet);

                foreach ($transactions as $transaction) {
                    $manager->persist($transaction);
                }
            }
        }

        $manager->flush();
    }

    private function makeProfile(User $user): UserProfile
    {
        $userProfile = new UserProfile();

        $userProfile->setUser($user);
        $userProfile->loadMetaData()->setAvatar(($user->getId() % 2 === 0) ? 'https://i.pravatar.cc/300' : '');
        $userProfile->loadMetaData()->setFirstName('First'.$user->getId());
        $userProfile->loadMetaData()->setLastName('Last'.$user->getId());
        $userProfile->loadMetaData()->setDob('1990-01-01');
        $userProfile->loadMetaData()->setBiography('This is a biography'.$user->getId());
        $userProfile->loadMetaData()->setGender(
            ($user->getId() % 2 === 0) ? UserProfileConstant::GENDER_MALE : UserProfileConstant::GENDER_FEMALE
        );

        return $userProfile;
    }

    private function makeClient(User $user): Client
    {
        $client = new Client();

        $client->setUser($user);
        $client->setCreditPackage($this->creditPackage);
        $client->loadMetaData()->setIsAutoRenew();
        $client->loadTimestamps()->setLastRenewAt(new DateTimeImmutable());

        return $client;
    }

    private function makeUserWallet(User $user): UserWallet
    {
        $userWallet = new UserWallet();

        $userWallet->setUser($user);
        $userWallet->loadMetaData()->setCredit(5000.00);

        return $userWallet;
    }

    private function makeUserWalletTransactions(UserWallet $userWallet): array
    {
        $transactions = [];

        for ($i = 0; $i < 10; $i++) {
            $userWalletTransaction = new UserWalletTransaction();

            $userWalletTransaction->setUserWallet($userWallet);
            $userWalletTransaction->loadMetaData()->setAmount(10.00);
            $userWalletTransaction->loadMetaData()->setCredit(10.00);
            $userWalletTransaction->loadMetaData()->setType(
                ($i % 2 === 0) ? UserWalletTransactionConstant::TYPE_INCREASE
                    : UserWalletTransactionConstant::TYPE_DECREASE
            );
            $userWalletTransaction->loadMetaData()->setJsonData(['data' => 'data']);
            $userWalletTransaction->loadTimestamps()->setLoggedAt(new DateTimeImmutable());

            $transactions[] = $userWalletTransaction;
        }

        return $transactions;
    }

    private function makeCreditPackage(): CreditPackage
    {
        $creditPackage = new CreditPackage('obsidian');

        $creditPackage->loadMetadata()->setName('Obsidian Package');
        $creditPackage->loadMetadata()->setDescription(
            'The Obsidian package offers essential features for personal use.'
        );
        $creditPackage->loadMetaData()->setPrice(500.00);
        $creditPackage->loadMetaData()->setCredit(5000.00);

        return $creditPackage;
    }

}
