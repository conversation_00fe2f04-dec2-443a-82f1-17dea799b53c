<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date 6/7/2025
 * @time 10:34 AM
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Class CreateAccountDTO
 *
 * Data Transfer Object for creating a new account
 */
final class CreateNewAccountDTO
{

    public ?string $firstName;

    public ?string $lastName;

    public ?string $userName;

    public ?string $email;

    public ?string $password;

    public ?string $role;

    public ?int $status;

    public ?int $partnerProgramId = null;

    public ?string $registrationIp = null;

}
