<?php

declare(strict_types=1);

namespace App\Base;

use App\Library\ReCaptcha\Contract\ReCaptchaServiceInterface;
use App\Library\ReCaptcha\ReCaptchaService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

/**
 * Abstract class BaseFormType.
 *
 * Base form type class that can be extended by other form types.
 * This class serves as a foundation for creating form types in Symfony applications.
 */
abstract class BaseFormType extends AbstractType
{

    /**
     * @var bool $hasCaptcha Defines whether the form has a captcha or not.
     */
    protected bool $hasCaptcha = false;

    /**
     * @var array $captchaData Contains data related to the captcha.
     */
    protected array $captchaData
        = [
            'action' => 'submit_form',
        ];

    /**
     * The ReCaptcha service instance.
     *
     * This service is used to handle Google reCAPTCHA functionality.
     *
     * @var ReCaptchaServiceInterface
     */
    private readonly ReCaptchaServiceInterface $reCaptchaService;

    public function __construct()
    {
        $this->reCaptchaService = new ReCaptchaService();
        $this->reCaptchaService->isEnable = $this->hasCaptcha;
    }

    /**
     * Builds the form.
     *
     * This method is called for each type in the hierarchy starting from the top most type.
     * Type extensions can further modify the form.
     *
     * @param  FormBuilderInterface  $builder  The form builder
     * @param  array                 $options  The options
     *
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if ($this->reCaptchaService->isEnable) {
            $builder->add(
                'recaptcha',
                HiddenType::class,
                [
                    'mapped' => false,
                    'attr'   => [
                        'class' => 'g-recaptcha',
                        'data-sitekey' => $this->reCaptchaService->getSiteKey(),
                    ],
                ]
            )->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
                $data = $event->getData();
                $form = $event->getForm();

                if (!isset($data['recaptcha'])
                    || !$this->reCaptchaService->validate(
                        $data['recaptcha'],
                        $this->captchaData
                    )
                ) {
                    $form->addError(new FormError('Invalid CAPTCHA token. Please try again.'));
                }
            });
        }
    }

}
