flasher:
    default: flasher

    main_script: '/vendor/flasher/flasher.min.js'

    styles:
        - '/vendor/flasher/flasher.min.css'

    inject_assets: true

    translate: true

    excluded_paths:
        - '/^\/_profiler/'
        - '/^\/_fragment/'

    flash_bag:
        success: [ 'success' ]
        error: [ 'error', 'danger' ]
        warning: [ 'warning', 'alarm' ]
        info: [ 'info', 'notice', 'alert' ]

    plugins:
        sweetalert:
            scripts:
                - '/vendor/flasher/sweetalert2.min.js'
                - '/vendor/flasher/flasher-sweetalert.min.js'
            styles:
                - '/vendor/flasher/sweetalert2.min.css'
            options:
                timer: 20000
