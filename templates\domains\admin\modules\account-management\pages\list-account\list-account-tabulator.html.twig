{% extends 'domains/admin/modules/account-management/pages/list-account/data-table.html.twig' %}

{% block account_list_tabulator_init %}
    <div id="users-table"></div>
{% endblock %}

{% block account_list_tabulator %}
    <script>
        // Custom Tabulator JS

        console.log('1')

        // Role definitions
        const ROLES = {
            'ROLE_ADMIN': { name: 'Admin', priority: 5, class: 'badge-admin' },
            'ROLE_RESELLER': { name: 'Reseller', priority: 4, class: 'badge-reseller' },
            'ROLE_CLIENT': { name: 'Client', priority: 3, class: 'badge-client' },
            'ROLE_PARTNER': { name: 'Partner', priority: 2, class: 'badge-partner' },
            'ROLE_USER': { name: 'User', priority: 1, class: 'badge-user' },
        }

        console.log('2')

        // Status definitions
        const STATUS = {
            0: { name: 'Inactive', class: 'badge rounded-full bg-slate-150 text-slate-800 dark:bg-navy-500 dark:text-navy-100' },
            1: { name: 'Active', class: 'badge rounded-full bg-success text-white' },
            2: { name: 'Blocked', class: 'badge rounded-full bg-warning text-white' },
            3: { name: 'Deleted', class: 'badge rounded-full bg-error text-white' },
        }

        // Tabulator configurations
        const mobileConfig = {
            layout: 'fitDataFill',
            responsiveLayout: 'collapse',
            rowHeader: {
                formatter: 'responsiveCollapse',
                width: 50,
                hozAlign: 'center',
                resizable: false,
                headerSort: false,
                responsive: 0,
                cellClick: function (
                    e) {
                    e.stopPropagation()
                },
            },
        }

        const desktopConfig = {
            layout: 'fitDataFill',
            responsiveLayout: false,
            rowHeader: {
                headerSort: false,
                resizable: false,
                frozen: true,
                width: 56,
                headerHozAlign: 'center',
                hozAlign: 'center',
                formatter: 'rowSelection',
                titleFormatter: 'rowSelection',
                cellClick: function (e) {
                    e.stopPropagation()
                },
            },
        }

        // Base Tabulator
        const baseConfig = {
            ajaxURL: '{{ url('app_admin_account_list_getList') }}',
            // height: '65vh',
            placeholder: 'No Accounts found',
            pagination: 'local',
            paginationSize: 10,
            paginationSizeSelector: false,
            selectableRows: true,
            groupBy: 'roles',
            columns: [
                {
                    title: '#',
                    formatter: 'rownum',
                    maxWidth: 52,
                    hozAlign: 'center',
                    headerSort: false,
                    responsive: 1,
                },
                {
                    title: 'Parent',
                    field: 'parent',
                    maxWidth: 120,
                    formatter: 'textarea',
                    hozAlign: 'left',
                    responsive: 1,
                },
                {
                    title: 'Username',
                    field: 'username',
                    responsive: 1,
                    formatter: function (cell) {
                        return `<span class="font-medium text-slate-700 dark:text-navy-100 whitespace-normal break-words">${cell.getValue()}</span>`
                    },
                },
                {
                    title: 'Email',
                    field: 'email',
                    maxWidth: 160,
                    formatter: 'textarea',
                    hozAlign: 'left',
                    responsive: 0,
                },
                {
                    title: 'Role',
                    field: 'roles',
                    maxWidth: 100,
                    hozAlign: 'center',
                    responsive: 0,
                    formatter: roleFormatter,
                },
                {
                    title: 'Last Purchased',
                    headerWordWrap: true,
                    field: 'last_purchased',
                    maxWidth: 125,
                    formatter: 'textarea',
                    headerSort: false,
                    responsive: 1,
                },
                {
                    title: 'Status',
                    field: 'status',
                    maxWidth: 120,
                    hozAlign: 'center',
                    formatter: function (cell) {
                        const statusValue = cell.getValue()
                        const statusInfo = getStatusInfo(statusValue)
                        return `<div class="${statusInfo.class}">${statusInfo.name}</div>`
                    },
                    responsive: 1,
                },
                {
                    title: 'Reg IP',
                    field: 'registration_ip',
                    maxWidth: 100,
                    formatter: 'textarea',
                    headerSort: false,
                    responsive: 1,
                },
                {
                    title: 'Last Login',
                    field: 'last_login_at',
                    headerWordWrap: true,
                    maxWidth: 120,
                    formatter: formatDateTime,
                    responsive: 1,
                },
                {
                    title: 'Blocked At',
                    field: 'blocked_at',
                    headerWordWrap: true,
                    maxWidth: 130,
                    formatter: formatDateTime,
                    responsive: 1,
                },
                {
                    title: 'Deleted At',
                    field: 'deleted_at',
                    headerWordWrap: true,
                    maxWidth: 130,
                    formatter: formatDateTime,
                    responsive: 1,
                },
                {
                    title: 'Created At',
                    field: 'created_at',
                    headerWordWrap: true,
                    maxWidth: 130,
                    formatter: formatDateTime,
                    responsive: 1,
                },
                {
                    title: 'Updated At',
                    field: 'updated_at',
                    headerWordWrap: true,
                    maxWidth: 130,
                    formatter: formatDateTime,
                    responsive: 1,
                },

                {
                    title: 'Action',
                    field: 'action',
                    maxWidth: 125,
                    headerSort: false,
                    hozAlign: 'center',
                    responsive: 0,
                    cellClick: function (e) {
                        e.stopPropagation()
                        e.preventDefault()
                        return false
                    },
                    formatter: function (cell) {
                        const rowData = cell.getRow().getData()
                        const status = parseInt(rowData.status)
                        const userId = rowData.id

                        // Tạo action items dựa trên status
                        let statusActions = ''

                        // Luôn có View và Renew (removed icons)
                        const commonActions = `
            <li><a href="javascript:void(0);" onclick="viewUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">View</a></li>
<!--            <li><a href="#" onclick="renewUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Renew</a></li>-->
        `

                        // Actions dựa trên status (removed all SVG icons)
                        switch (status) {
                            case 0: // Inactive
                                statusActions = `
                    <li><a href="#" onclick="activateUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Activate</a></li>
                    <li><a href="#" onclick="blockUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Block</a></li>
                `
                                break

                            case 1: // Active
                                statusActions = `
                    <li><a href="#" onclick="deactivateUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Deactivate</a></li>
                    <li><a href="#" onclick="blockUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Block</a></li>
                `
                                break

                            case 2: // Blocked
                                statusActions = `
                    <li><a href="#" onclick="unblockUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Unblock</a></li>
                `
                                break

                            case 3: // Deleted
                                statusActions = `
                    <li><a href="#" onclick="restoreUser(${userId})" class="flex items-center px-3 h-8 pr-12 font-medium tracking-wide outline-hidden transition-all hover:bg-slate-100 hover:text-slate-800 focus:bg-slate-100 focus:text-slate-800 dark:hover:bg-navy-600 dark:hover:text-navy-100 dark:focus:bg-navy-600 dark:focus:text-navy-100">Restore</a></li>
                `
                                break

                            default:
                                statusActions = ''
                        }

                        return `<div x-data="usePopper({placement:'bottom-end',offset:4})" @click.outside="isShowPopper && (isShowPopper = false)" @click.stop class="inline-flex">
            <button x-ref="popperRef" @click.stop="isShowPopper = !isShowPopper" class="btn size-8 rounded-full p-0 hover:bg-slate-300/20 focus:bg-slate-300/20 active:bg-slate-300/25 dark:hover:bg-navy-300/20 dark:focus:bg-navy-300/20 dark:active:bg-navy-300/25">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                </svg>
            </button>
            <div x-ref="popperRoot" class="popper-root" :class="isShowPopper && 'show'">
                <div class="popper-box rounded-md border border-slate-150 bg-white py-1.5 font-inter dark:border-navy-500 dark:bg-navy-700">
                    <ul>
                        ${commonActions}
                    </ul>
                    <div class="my-1 h-px bg-slate-150 dark:bg-navy-500"></div>
                    <ul>
                        ${statusActions}
                    </ul>
                </div>
            </div>
        </div>`
                    },
                },
            ],
            // Event handlers for selection changes
            rowSelectionChanged: function (data, rows) {
                // This will be triggered by programmatic selection
                updateHeaderCheckbox()
            },
            // Event fired when data is loaded
            dataLoaded: function () {
                updateHeaderCheckbox()
                updateCheckboxes()
            },
            // Event fired when table is redrawn
            tableBuilt: function () {
                updateHeaderCheckbox()
            },
        }

        var selectedRoleForBulkChange = null
        var table
        var bulkTimeout = null

        // Initialize table
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize table with responsive config
            initializeTable()

            // Initialize search functionality (header search input)
            initializeSearchFunctionality()

            // Initialize filter functionality - ADD THIS LINE
            initializeFilterFunctionality()

            // Page size selector
            document.getElementById('page-size').
                addEventListener('change', function () {
                    table.setPageSize(parseInt(this.value))
                })

            // Initialize pagination on load
            setTimeout(() => {
                updatePagination()
            }, 100)
        })

        //// Specific functions

        // Get the highest priority role from array
        function getHighestRole (rolesArray) {
            if (!Array.isArray(rolesArray) || rolesArray.length === 0) {
                return { role: 'ROLE_USER', name: 'User', class: 'badge-user' }
            }

            let highestRole = null
            let highestPriority = 0

            rolesArray.forEach(role => {
                const roleKey = role.startsWith('ROLE_')
                    ? role
                    : `ROLE_${role.toUpperCase()}`
                if (ROLES[roleKey] && ROLES[roleKey].priority > highestPriority) {
                    highestPriority = ROLES[roleKey].priority
                    highestRole = roleKey
                }
            })

            if (!highestRole) {
                return { role: 'ROLE_USER', name: 'User', class: 'badge-user' }
            }

            return {
                role: highestRole,
                name: ROLES[highestRole].name,
                class: ROLES[highestRole].class,
            }
        }

        // Get status information
        function getStatusInfo (statusValue) {
            const status = parseInt(statusValue)
            return STATUS[status] || STATUS[0] // Default to Inactive if unknown
        }

        // Action handler functions
        function viewUser (userId) {
            // View action - no modal, redirect to view page
            // window.location.href = `/admin/users/${userId}/view`
            // Or if you want to open in new tab:
            // window.open(`/admin/users/${userId}/view`, '_blank');
            window.location.href = "{{ path('app_admin_account_detail', {'id': 'USER_ID'}) }}".replace('USER_ID', userId)
        }

        function renewUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Renew User Subscription',
                message: `Do you want to renew subscription for user <strong>${rowData.username}</strong>?<br><br>
                  <div class="text-left">
                      <strong>Current Details:</strong><br>
                      • Email: ${rowData.email}<br>
                      • Last Purchase: ${rowData.last_purchased || 'N/A'}<br>
                      • Status: ${getStatusInfo(rowData.status).name}
                  </div>`,
                type: 'info',
                action: 'renew',
                userId: userId,
            })
        }

        function activateUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Activate User',
                message: `Are you sure you want to activate user <strong>${rowData.username}</strong>?<br><br>
                  This will change the user status to <span class="badge bg-success text-white">Active</span>`,
                type: 'success',
                action: 'activate',
                userId: userId,
            })
        }

        function deactivateUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Deactivate User',
                message: `Are you sure you want to deactivate user <strong>${rowData.username}</strong>?<br><br>
                  This will change the user status to <span class="badge bg-slate-150 text-slate-800">Inactive</span>`,
                type: 'warning',
                action: 'deactivate',
                userId: userId,
            })
        }

        function blockUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Block User',
                message: `Are you sure you want to block user <strong>${rowData.username}</strong>?<br><br>
                  This will prevent the user from accessing their account and change status to <span class="badge bg-warning text-white">Blocked</span>`,
                type: 'warning',
                action: 'block',
                userId: userId,
            })
        }

        function unblockUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Unblock User',
                message: `Are you sure you want to unblock user <strong>${rowData.username}</strong>?<br><br>
                  This will restore user access and change status to <span class="badge bg-success text-white">Active</span>`,
                type: 'success',
                action: 'unblock',
                userId: userId,
            })
        }

        function restoreUser (userId) {
            const rowData = table.getRow(userId).getData()
            showActionModal({
                title: 'Restore User',
                message: `Are you sure you want to restore user <strong>${rowData.username}</strong>?<br><br>
                  This will restore the deleted user and change status to <span class="badge bg-success text-white">Active</span>`,
                type: 'success',
                action: 'restore',
                userId: userId,
            })
        }

        // Execute action after confirmation
        function executeAction (action, userId) {
            console.log(`Executing ${action} for user ${userId}`)

            // Handle bulk actions
            if (action.startsWith('bulk-')) {
                executeBulkAction(action, userId) // userId sẽ là array trong trường hợp bulk
                return
            }

            // Existing single action code...
            const row = table.getRow(userId)
            const rowData = row.getData()

            // Update table data based on action
            switch (action) {
                case 'activate':
                    makeApiCall('activate', userId)
                    break

                case 'deactivate':
                    makeApiCall('deactivate', userId)
                    break

                case 'block':
                    makeApiCall('block', userId)
                    break

                case 'unblock':
                    makeApiCall('unblock', userId)
                    break

                case 'restore':
                    makeApiCall('restore', userId)
                    break
            }

        }

        // Bulk Action Functions
        function bulkActivate () {
            const selectedRows = table.getSelectedRows()
            if (selectedRows.length === 0) {
                alert('Please select users to activate')
                return
            }

            const usernames = selectedRows.map(row => row.getData().username)
            const displayNames = usernames.length > 3
                ? `${usernames.slice(0, 3).join(', ')} and ${usernames.length -
                3} others`
                : usernames.join(', ')

            showActionModal({
                title: 'Bulk Activate Users',
                message: `Are you sure you want to activate <strong>${selectedRows.length}</strong> users?<br><br>
                  <div class="text-left">
                      <strong>Selected Users:</strong><br>
                      ${displayNames}<br><br>
                      This will change their status to <span class="badge bg-success text-white">Active</span>
                  </div>`,
                type: 'success',
                action: 'bulk-activate',
                userId: selectedRows.map(row => row.getData().id),
            })
            closeMobileDropdown()
        }

        function bulkDeactivate () {
            const selectedRows = table.getSelectedRows()
            if (selectedRows.length === 0) {
                alert('Please select users to deactivate')
                return
            }

            const usernames = selectedRows.map(row => row.getData().username)
            const displayNames = usernames.length > 3
                ? `${usernames.slice(0, 3).join(', ')} and ${usernames.length -
                3} others`
                : usernames.join(', ')

            showActionModal({
                title: 'Bulk Deactivate Users',
                message: `Are you sure you want to deactivate <strong>${selectedRows.length}</strong> users?<br><br>
                  <div class="text-left">
                      <strong>Selected Users:</strong><br>
                      ${displayNames}<br><br>
                      This will change their status to <span class="badge bg-slate-150 text-slate-800">Inactive</span>
                  </div>`,
                type: 'warning',
                action: 'bulk-deactivate',
                userId: selectedRows.map(row => row.getData().id),
            })
            closeMobileDropdown()
        }

        function bulkBlock () {
            const selectedRows = table.getSelectedRows()
            if (selectedRows.length === 0) {
                alert('Please select users to block')
                return
            }

            const usernames = selectedRows.map(row => row.getData().username)
            const displayNames = usernames.length > 3
                ? `${usernames.slice(0, 3).join(', ')} and ${usernames.length -
                3} others`
                : usernames.join(', ')

            showActionModal({
                title: 'Bulk Block Users',
                message: `Are you sure you want to block <strong>${selectedRows.length}</strong> users?<br><br>
                  <div class="text-left">
                      <strong>Selected Users:</strong><br>
                      ${displayNames}<br><br>
                      This will prevent them from accessing their accounts and change status to <span class="badge bg-warning text-white">Blocked</span>
                  </div>`,
                type: 'warning',
                action: 'bulk-block',
                userId: selectedRows.map(row => row.getData().id),
            })
            closeMobileDropdown()
        }

        function bulkUnblock () {
            const selectedRows = table.getSelectedRows()
            if (selectedRows.length === 0) {
                alert('Please select users to unblock')
                return
            }

            const usernames = selectedRows.map(row => row.getData().username)
            const displayNames = usernames.length > 3
                ? `${usernames.slice(0, 3).join(', ')} and ${usernames.length -
                3} others`
                : usernames.join(', ')

            showActionModal({
                title: 'Bulk Unblock Users',
                message: `Are you sure you want to unblock <strong>${selectedRows.length}</strong> users?<br><br>
                  <div class="text-left">
                      <strong>Selected Users:</strong><br>
                      ${displayNames}<br><br>
                      This will restore their access and change status to <span class="badge bg-success text-white">Active</span>
                  </div>`,
                type: 'success',
                action: 'bulk-unblock',
                userId: selectedRows.map(row => row.getData().id),
            })
            closeMobileDropdown()
        }

        // Execute bulk action after confirmation
        function executeBulkAction (action, userIds) {
            // console.log(`Executing ${action} for users:`, userIds)

            const actionMap = {
                'bulk-activate': { status: 1, apiAction: 'activate', message: 'activated' },
                'bulk-deactivate': { status: 0, apiAction: 'deactivate', message: 'deactivated' },
                'bulk-block': { status: 2, apiAction: 'block', message: 'blocked' },
                'bulk-unblock': { status: 1, apiAction: 'unblock', message: 'unblocked' },
                'bulk-delete': { status: 3, apiAction: 'delete', message: 'deleted' },
            }

            const actionConfig = actionMap[action]
            if (!actionConfig) {
                return
            }

            // Call Bulk Api
            makeBulkApiCall(actionConfig.apiAction, userIds)

            // Clear selection after bulk action
            table.deselectRow()

        }

        // Execute renew action (special handling)
        function executeRenewAction (userId) {

            // Redirect to renew page or open renew modal
            window.location.href = `/admin/users/${userId}/renew`

            // Or if you want to handle it differently:
            // makeApiCall('renew', userId);
        }

        //// Reusable functions

        // DateTime formater
        function formatDateTime (cell) {
            const value = cell.getValue()

            // Return empty string if no value
            if (!value || value === '' || value === null) {
                return ''
            }

            try {
                // Parse the date_atom format (ISO 8601)
                const isoRegex = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})$/
                const match = value.match(isoRegex)

                if (!match) {
                    return value
                }

                const [, year, month, day, hours, minutes, seconds] = match

                // Check if we're in mobile view
                const isMobileView = isMobile()

                if (isMobileView) {
                    // Mobile: single line format
                    return `<div class="text-xs text-slate-700 dark:text-navy-100">
                        ${year}-${month}-${day} ${hours}:${minutes}:${seconds}
                    </div>`
                } else {
                    // Desktop: two line format
                    return `<div class="text-xs leading-tight">
                        <div class="font-medium text-slate-700 dark:text-navy-100">${year}-${month}-${day}</div>
                        <div class="text-slate-500 dark:text-navy-300">${hours}:${minutes}:${seconds}</div>
                    </div>`
                }
            } catch (error) {
                console.error('Error formatting datetime:', error)
                return value
            }
        }

        // Role formatter
        function roleFormatter (cell) {
            const rolesData = cell.getValue()

            // Handle both array and string formats
            let roles = []
            if (Array.isArray(rolesData)) {
                roles = rolesData
            } else if (typeof rolesData === 'string') {
                try {
                    roles = JSON.parse(rolesData)
                } catch (e) {
                    roles = [rolesData]
                }
            }

            const highestRole = getHighestRole(roles)
            return `<div class="badge ${highestRole.class}">${highestRole.name}</div>`
        }

        // Screen check function
        function isMobile () {
            const width = window.innerWidth || document.documentElement.clientWidth ||
                document.body.clientWidth
            return width < 992
        }

        function initializeTable () {
            // Determine if we're on mobile or desktop
            const isMobileView = isMobile()
            const newLayout = isMobileView ? 'mobile' : 'desktop'

            // Create the configuration by combining base config with responsive config
            let config

            if (isMobileView) {
                config = {
                    ...baseConfig,
                    ...mobileConfig,
                }
            } else {
                config = {
                    ...baseConfig,
                    ...desktopConfig,
                }
            }
            console.log('Current layout:', isMobileView, 'New layout:', newLayout,
                'Window width:', window.innerWidth)

            // If table exists, destroy it and recreate
            // if (table) {
            //     table.redraw(true)
            // } else {
            //     table = new Tabulator('#users-table', config)
            // }

            table = new Tabulator('#users-table', config)

            // Update the current layout
            currentLayout = newLayout

            // Re-attach event listeners
            attachTableEvents()
        }

        function updateBulkActions (selectedRows) {
            const bulkActionsElement = document.getElementById('bulk-actions')
            const selectedCountElement = document.getElementById('selected-count')

            // Clear timeout cũ để tránh conflict
            if (bulkTimeout) {
                clearTimeout(bulkTimeout)
                bulkTimeout = null
            }

            if (selectedRows.length > 0) {
                // Update count
                selectedCountElement.textContent = selectedRows.length

                // Show bulk actions
                bulkActionsElement.style.display = 'block'

                // Delay một chút để CSS transition hoạt động
                bulkTimeout = setTimeout(() => {
                    bulkActionsElement.classList.add('show')
                }, 10)

            } else {
                // Hide bulk actions
                bulkActionsElement.classList.remove('show')

                // Hide element sau khi animation xong
                bulkTimeout = setTimeout(() => {
                    bulkActionsElement.style.display = 'none'
                }, 300)
            }
        }

        function clearSelection () {
            table.deselectRow()
            closeMobileDropdown()
        }

        // Helper function to close mobile dropdown
        function closeMobileDropdown () {
            // Find the Alpine.js component and close dropdown
            const bulkActionsElement = document.getElementById('bulk-actions')
            if (bulkActionsElement && bulkActionsElement.__x) {
                bulkActionsElement.__x.$data.isMobileDropdownOpen = false
            }
        }

        // Helper function to show action modal
        function showActionModal (modalData) {
            // Dispatch event to Alpine.js component
            window.dispatchEvent(new CustomEvent('open-action-modal', {
                detail: modalData,
            }))
        }

        // Helper function to get confirm button text
        function getConfirmButtonText (action) {
            const buttonTexts = {
                // Single actions
                'activate': 'Activate User',
                'deactivate': 'Deactivate User',
                'block': 'Block User',
                'unblock': 'Unblock User',
                'restore': 'Restore User',

                // Bulk actions
                'bulk-activate': 'Activate Users',
                'bulk-deactivate': 'Deactivate Users',
                'bulk-block': 'Block Users',
                'bulk-unblock': 'Unblock Users',
                'bulk-delete': 'Delete Users',
            }
            return buttonTexts[action] || 'Confirm'
        }

        // Make API calls to backend
        function makeApiCall (action, userId) {
            // Replace with your actual API endpoints
            fetch(`/admin/manage/account/list/${action}/${userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    action: action,
                    userId: userId,
                }),
            }).then(response => response.json()).then(data => {
                if (data.success) {
                    window.location.reload()
                } else {
                    window.location.reload()
                }
            }).catch(error => {
                window.location.reload()
            })
        }

        // Make bulk API calls to backend
        function makeBulkApiCall (action, userId) {
            // Replace with your actual bulk API endpoint
            fetch(`/admin/manage/account/list/bulk/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '{{ csrf_token("bulk-action") }}',
                },
                body: JSON.stringify({
                    action: action,
                    userIds: userId,
                }),
            }).then(response => response.json()).then(data => {
                if (data.success) {
                    window.location.reload()
                } else {
                    window.location.reload()
                }
            }).catch(error => {
                window.location.reload()
            })
        }

        // Custom pagination
        function updatePagination () {
            const currentPage = table.getPage()
            const maxPage = table.getPageMax()
            const pageSize = table.getPageSize()
            const activeRowCount = table.getDataCount('active')

            // For grouped data, we need to count visible rows differently
            let visibleRowCount
            let totalDataCount

            if (table.getGroups().length > 0) {
                // When data is grouped, count visible rows
                visibleRowCount = table.getRows('visible').length
                totalDataCount = table.getDataCount()

                // Calculate actual displayed rows (excluding group headers)
                const groupHeaders = table.getGroups().length
                const actualVisibleRows = visibleRowCount - groupHeaders

            } else {
                // Normal pagination calculation
                visibleRowCount = table.getRows('visible').length
                totalDataCount = table.getDataCount()
            }

            document.getElementById(
                'table-info').textContent = `Total of Row: ${activeRowCount}`

            // Update pagination buttons
            const pagination = document.getElementById('pagination')
            let paginationHTML = '<ol class="pagination flex items-center space-x-0">'

            // Previous button
            const prevDisabled = currentPage === 1 || maxPage === 0
            paginationHTML += `
        <li class="rounded-l-lg bg-slate-150 dark:bg-navy-500">
            <button onclick="${prevDisabled ? '' : 'table.previousPage()'}"
                    ${prevDisabled ? 'disabled' : ''}
                    class="flex size-8 items-center justify-center rounded-lg text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90 ${prevDisabled
                ? 'opacity-50 cursor-not-allowed'
                : ''}">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
        </li>`

            // Show page numbers only if we have pages
            if (maxPage > 0) {
                // Calculate which page numbers to show
                let startPage = Math.max(1, currentPage - 2)
                let endPage = Math.min(maxPage, currentPage + 2)

                // Adjust if we're near the beginning or end
                if (endPage - startPage < 4) {
                    if (startPage === 1) {
                        endPage = Math.min(maxPage, startPage + 4)
                    } else if (endPage === maxPage) {
                        startPage = Math.max(1, endPage - 4)
                    }
                }

                // First page and dots if needed
                if (startPage > 1) {
                    paginationHTML += `
                <li class="bg-slate-150 dark:bg-navy-500">
                    <button onclick="table.setPage(1)" class="flex h-8 min-w-[2rem] items-center justify-center rounded-lg px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">1</button>
                </li>`

                    if (startPage > 2) {
                        paginationHTML += `
                    <li class="bg-slate-150 dark:bg-navy-500">
                        <span class="flex h-8 min-w-[2rem] items-center justify-center px-3">...</span>
                    </li>`
                    }
                }

                // Page numbers
                for (let i = startPage; i <= endPage; i++) {
                    if (i === currentPage) {
                        paginationHTML += `
                    <li class="bg-slate-150 dark:bg-navy-500">
                        <button class="flex h-8 min-w-[2rem] items-center justify-center rounded-lg bg-primary px-3 leading-tight text-white transition-colors hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90">${i}</button>
                    </li>`
                    } else {
                        paginationHTML += `
                    <li class="bg-slate-150 dark:bg-navy-500">
                        <button onclick="table.setPage(${i})" class="flex h-8 min-w-[2rem] items-center justify-center rounded-lg px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">${i}</button>
                    </li>`
                    }
                }

                // Last page and dots if needed
                if (endPage < maxPage) {
                    if (endPage < maxPage - 1) {
                        paginationHTML += `
                    <li class="bg-slate-150 dark:bg-navy-500">
                        <span class="flex h-8 min-w-[2rem] items-center justify-center px-3">...</span>
                    </li>`
                    }

                    paginationHTML += `
                <li class="bg-slate-150 dark:bg-navy-500">
                    <button onclick="table.setPage(${maxPage})" class="flex h-8 min-w-[2rem] items-center justify-center rounded-lg px-3 leading-tight transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90">${maxPage}</button>
                </li>`
                }
            }

            // Next button
            const nextDisabled = currentPage === maxPage || maxPage === 0
            paginationHTML += `
        <li class="rounded-r-lg bg-slate-150 dark:bg-navy-500">
            <button onclick="${nextDisabled ? '' : 'table.nextPage()'}"
                    ${nextDisabled ? 'disabled' : ''}
                    class="flex size-8 items-center justify-center rounded-lg text-slate-500 transition-colors hover:bg-slate-300 focus:bg-slate-300 active:bg-slate-300/80 dark:text-navy-200 dark:hover:bg-navy-450 dark:focus:bg-navy-450 dark:active:bg-navy-450/90 ${nextDisabled
                ? 'opacity-50 cursor-not-allowed'
                : ''}">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </li>`

            paginationHTML += '</ol>'
            pagination.innerHTML = paginationHTML
        }

        function attachTableEvents () {
            // Event handlers for selection changes
            table.on('rowSelectionChanged', function (data, rows) {
                updateBulkActions(rows)
            })

            // Event fired when data is loaded
            table.on('dataLoaded', function () {
                setTimeout(() => {
                    updatePagination()
                }, 100)
            })

            table.on('dataChanged', function () {
                setTimeout(() => {
                    updatePagination()
                }, 50)
            })

            table.on('pageChanged', function () {
                updatePagination()
            })

            table.on('pageLoaded', function () {
                setTimeout(() => {
                    updatePagination()
                }, 50)
            })

            // Group toggle event listener
            table.on('groupToggled', function (group) {
                // Update pagination when groups are opened/closed
                setTimeout(() => {
                    updatePagination()
                }, 50)
            })

            // Listen for group visibility changes
            table.on('groupVisibilityChanged', function (group, visible) {
                setTimeout(() => {
                    updatePagination()
                }, 50)
            })
        }

        // Filter Functions
        function applyFilters () {
            // Get text filter values
            const parent = document.getElementById('filter-parent').value.trim()
            const username = document.getElementById('filter-username').value.trim()
            const email = document.getElementById('filter-email').value.trim()

            // Get dropdown filter values
            const role = document.getElementById('filter-role').value
            const status = document.getElementById('filter-status').value

            // Get date range values
            const lastLoginRange = document.getElementById(
                'filter-last-login-range').value
            const blockedAtRange = document.getElementById(
                'filter-blocked-at-range').value
            const createdAtRange = document.getElementById(
                'filter-created-at-range').value
            const updatedAtRange = document.getElementById(
                'filter-updated-at-range').value

            // Clear existing filters
            table.clearFilter()

            // Apply text filters
            if (parent) {
                table.addFilter('parent', 'like', parent)
            }
            if (username) {
                table.addFilter('username', 'like', username)
            }
            if (email) {
                table.addFilter('email', 'like', email)
            }

            // Apply dropdown filters
            if (role) {
                table.addFilter('roles', 'like', role)
            }
            if (status !== '') {
                table.addFilter('status', '=', parseInt(status))
            }

            // Apply date range filters
            if (lastLoginRange) {
                const dates = parseDateRange(lastLoginRange)
                if (dates.from) {
                    table.addFilter('last_login_at', '>=', dates.from + 'T00:00:00')
                }
                if (dates.to) {
                    table.addFilter('last_login_at', '<=', dates.to + 'T23:59:59')
                }
            }

            if (blockedAtRange) {
                const dates = parseDateRange(blockedAtRange)
                if (dates.from) {
                    table.addFilter('blocked_at', '>=', dates.from + 'T00:00:00')
                }
                if (dates.to) {
                    table.addFilter('blocked_at', '<=', dates.to + 'T23:59:59')
                }
            }

            if (createdAtRange) {
                const dates = parseDateRange(createdAtRange)
                if (dates.from) {
                    table.addFilter('created_at', '>=', dates.from + 'T00:00:00')
                }
                if (dates.to) {
                    table.addFilter('created_at', '<=', dates.to + 'T23:59:59')
                }
            }

            if (updatedAtRange) {
                const dates = parseDateRange(updatedAtRange)
                if (dates.from) {
                    table.addFilter('updated_at', '>=', dates.from + 'T00:00:00')
                }
                if (dates.to) {
                    table.addFilter('updated_at', '<=', dates.to + 'T23:59:59')
                }
            }

            console.log('Filters applied:', {
                parent, username, email, role, status,
                lastLoginRange, blockedAtRange, createdAtRange, updatedAtRange,
            })

            // Update pagination after filtering
            setTimeout(() => {
                updatePagination()
            }, 100)
        }

        function clearFilters () {
            // Clear all text filter inputs
            document.getElementById('filter-parent').value = ''
            document.getElementById('filter-username').value = ''
            document.getElementById('filter-email').value = ''

            // Clear dropdown filters
            document.getElementById('filter-role').value = ''
            document.getElementById('filter-status').value = ''

            // Clear date range filters
            const dateRangeFields = [
                'filter-last-login-range',
                'filter-blocked-at-range',
                'filter-created-at-range',
                'filter-updated-at-range',
            ]

            dateRangeFields.forEach(fieldId => {
                const field = document.getElementById(fieldId)
                if (field) {
                    if (field._x_flatpickr) {
                        field._x_flatpickr.clear()
                    }
                    field.value = ''
                }
            })

            // Clear table filters
            table.clearFilter()

            // Update pagination after clearing filters
            setTimeout(() => {
                updatePagination()
            }, 100)

            console.log('All filters cleared')
        }

        // Helper function to parse date range from flatpickr
        function parseDateRange (dateRangeString) {
            if (!dateRangeString || dateRangeString.trim() === '') {
                return { from: null, to: null }
            }

            // flatpickr in range mode returns dates separated by " to "
            const dates = dateRangeString.split(' to ')

            return {
                from: dates[0] ? dates[0].trim() : null,
                to: dates[1] ? dates[1].trim() : (dates[0] ? dates[0].trim() : null),
            }
        }

        // Initialize filter functionality
        function initializeFilterFunctionality () {
            // Add real-time filtering for text inputs (optional)
            const textFilters = ['filter-parent', 'filter-username', 'filter-email']

            textFilters.forEach(filterId => {
                const filterElement = document.getElementById(filterId)
                if (filterElement) {
                    // Debounce function for real-time filtering
                    let filterTimeout
                    filterElement.addEventListener('input', function () {
                        clearTimeout(filterTimeout)
                        filterTimeout = setTimeout(() => {
                            // Auto-apply filters as user types (optional)
                            // applyFilters()
                        }, 500)
                    })
                }
            })

            // Add change event listeners for dropdown filters
            const dropdownFilters = ['filter-role', 'filter-status']

            dropdownFilters.forEach(filterId => {
                const filterElement = document.getElementById(filterId)
                if (filterElement) {
                    filterElement.addEventListener('change', function () {
                        // Auto-apply filters when dropdown changes (optional)
                        // applyFilters()
                    })
                }
            })

            // Initialize flatpickr for date ranges if not already initialized
            const dateRangeFields = [
                'filter-last-login-range',
                'filter-blocked-at-range',
                'filter-created-at-range',
                'filter-updated-at-range',
            ]

            dateRangeFields.forEach(fieldId => {
                const field = document.getElementById(fieldId)
                if (field && !field._x_flatpickr) {
                    // Initialize flatpickr manually if Alpine.js didn't initialize it
                    if (typeof flatpickr !== 'undefined') {
                        field._x_flatpickr = flatpickr(field, {
                            mode: 'range',
                            dateFormat: 'Y-m-d',
                            allowInput: true,
                            onChange: function (selectedDates, dateStr, instance) {
                                // Auto-apply filters when date changes (optional)
                                // applyFilters()
                            },
                        })
                    }
                }
            })
        }

        // Update search functionality to use the search-all field
        function initializeSearchFunctionality () {
            // Global search (existing search input in header)
            const searchInput = document.getElementById('search-input')
            if (searchInput) {
                let searchTimeout
                searchInput.addEventListener('input', function () {
                    const searchValue = this.value.trim()

                    clearTimeout(searchTimeout)
                    searchTimeout = setTimeout(() => {
                        if (searchValue) {
                            table.setFilter([
                                [
                                    { field: 'parent', type: 'like', value: searchValue },
                                    { field: 'username', type: 'like', value: searchValue },
                                    { field: 'email', type: 'like', value: searchValue },
                                    { field: 'role', type: 'like', value: searchValue },
                                    { field: 'last_purchase', type: 'like', value: searchValue },
                                    { field: 'status', type: 'like', value: searchValue },
                                    { field: 'registration_ip', type: 'like', value: searchValue },
                                    { field: 'last_login', type: 'like', value: searchValue },
                                    { field: 'blocked_at', type: 'like', value: searchValue },
                                    { field: 'deleted_at', type: 'like', value: searchValue },
                                    { field: 'created_at', type: 'like', value: searchValue },
                                    { field: 'updated_at', type: 'like', value: searchValue },
                                ],
                            ])
                        } else {
                            table.clearFilter()
                        }

                        // Update pagination after search
                        setTimeout(() => {
                            updatePagination()
                        }, 100)
                    }, 300)
                })
            }
        }

        //Responsive
        window.addEventListener('resize', function () {
            let resizeTimer

            clearTimeout(resizeTimer)
            resizeTimer = setTimeout(function () {
                const newLayout = isMobile() ? 'mobile' : 'desktop'
                document.getElementById('list-account-create-button').textContent =
                    window.innerWidth > 992 ? 'Create Account' : 'Create'
                // Only reinitialize if the layout type has changed
                if (newLayout !== currentLayout) {
                    console.log('Layout changed from', currentLayout, 'to', newLayout)
                    initializeTable()
                } else {
                    if (table) {
                        console.log('Same layout, just redrawing')
                        table.redraw(true)
                    }
                }
            }, 150)
        })
    </script>
{% endblock %}
