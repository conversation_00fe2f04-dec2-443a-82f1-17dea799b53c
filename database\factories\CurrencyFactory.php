<?php

declare(strict_types=1);

namespace Database\Factory;

use App\Entity\Currency;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

/**
 * Class CurrencyFactory.
 */
class CurrencyFactory extends Fixture
{

    public function load(ObjectManager $manager): void
    {
        $this->addDefaultCurrency($manager);
    }

    private function addDefaultCurrency(ObjectManager $manager): void
    {
        $currency = new Currency('USD');

        $currency->loadMetadata()->setName('US Dollar');
        $currency->loadMetadata()->setSymbol('$');

        $manager->persist($currency);
        $manager->flush();
    }

}
