<?php

declare(strict_types=1);

namespace App\Utils\Helper;

/**
 * Class ArrayHelper.
 *
 * A utility class that provides helper methods for working with arrays.
 * This class includes methods for manipulating and querying arrays.
 */
class ArrayHelper
{

    /**
     * Unsets a key from a multidimensional array recursively.
     *
     * @param  array  $array        The multidimensional array to modify.
     * @param  mixed  $unwantedKey  The key to unset from the array. This can be a string or an integer.
     *
     * @return void
     */
    public static function recursiveUnset(array &$array, mixed $unwantedKey): void
    {
        unset($array[$unwantedKey]);

        foreach ($array as &$value) {
            if (is_array($value)) {
                self::recursiveUnset($value, $unwantedKey);
            }
        }
    }

    /**
     * Checks if a key exists in a multidimensional array.
     *
     * @param  array  $needle  The multidimensional array to search in.
     * @param  mixed  $keys    The keys to search for, provided as an array.
     * @param  int    $i       The current index in the keys array, used for recursion.
     *
     * @return bool
     */
    private function recursiveIsset(array $needle, mixed $keys, int $i = 0): bool
    {
        foreach ($needle as $key1 => $array1) {
            if ($key1 === $keys[$i]) {
                if (!is_array($array1)) {
                    return true;
                }

                return self::recursiveIsset($array1, $keys, ++$i);
            }
        }

        return false;
    }

}
