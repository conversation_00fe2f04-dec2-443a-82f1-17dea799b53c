<?php

declare(strict_types=1);

namespace App\Exception;

use BadMethodCallException;

/**
 * Class InvalidMethodCallException.
 *
 * This exception is thrown when a method is called that does not exist or is not valid
 * for the current context. It extends the BadMethodCallException to provide more specific
 * error handling for method calls.
 */
class InvalidMethodCallException extends BadMethodCallException
{

}
