<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    11/06/2025
 * @time    19:27
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Service;

use App\Domain\Admin\Module\Auth\Contract\WriteInfoLogServiceInterface;
use App\Domain\Admin\Module\Auth\DTO\AdminSecurityInfoLogDTO;
use App\Entity\Constant\LogConstant;
use App\Entity\Log;
use App\Repository\LogRepository;
use DateTimeImmutable;

/**
 * Class WriteInfoLogService.
 *
 * This service is responsible for writing info logs related to admin security events.
 * It implements the WriteInfoLogServiceInterface to ensure that it adheres to the contract
 * for writing info logs.
 */
class WriteInfoLogService implements WriteInfoLogServiceInterface
{

    private const string CHANNEL = 'admin_security';

    private LogRepository $logRepository;

    public function __construct(
        LogRepository $logRepository,
    ) {
        $this->logRepository = $logRepository;
    }

    /**
     * @inheritDoc
     */
    public function write(AdminSecurityInfoLogDTO $dto): bool
    {
        $securityLog = $this->transformDtoToEntity($dto);

        if ($this->logRepository->save($securityLog)) {
            return true;
        }

        return false;
    }

    /**
     * Transforms the provided AdminSecurityInfoLogDTO into an AdminSecurityLogModel entity.
     *
     * @param AdminSecurityInfoLogDTO $dto
     *
     * @return Log
     */
    private function transformDtoToEntity(AdminSecurityInfoLogDTO $dto): Log
    {
        $log = new Log();

        $log->setChannel(self::CHANNEL)
            ->loadMetaData()->setLevel(LogConstant::LOG_LEVEL_INFO)
            ->setContext($dto->context)
            ->setMessage($dto->message)
            ->setExtra($dto->extra);
        $log->loadLogTimestamps()->setLoggedAt(new DateTimeImmutable());

        return $log;
    }

}
