<?php

declare(strict_types=1);

namespace App\Entity\Trait;

use Doctrine\Common\Collections\Collection;

/**
 * Trait HasCollectionTrait.
 *
 * This trait is used to define some common method to manage collection of entities.
 */
trait HasCollectionTrait
{

    /**
     * Adds an entity to a collection if it is not already present.
     *
     * @param  Collection  $collection  The collection to add the entity to
     * @param  object      $entity      The entity to add
     *
     * @return static
     */
    public function addToCollection(Collection $collection, object $entity): static
    {
        if (!$collection->contains($entity)) {
            $collection->add($entity);
        }

        return $this;
    }

    /**
     * Adds multiple entities to a collection if they are not already present.
     *
     * @param  Collection  $collection  The collection to add the entities to
     * @param  array       $entities    The entities to add
     *
     * @return static
     */
    public function addManyToCollection(Collection $collection, array $entities): static
    {
        foreach ($entities as $entity) {
            if (!$collection->contains($entity)) {
                $collection->add($entity);
            }
        }

        return $this;
    }

    /**
     * Removes an entity from a collection if it is present.
     *
     * @param  Collection  $collection  The collection to remove the entity from
     * @param  object      $entity      The entity to remove
     *
     * @return static
     */
    public function removeFromCollection(Collection $collection, object $entity): static
    {
        if ($collection->contains($entity)) {
            $collection->removeElement($entity);
        }

        return $this;
    }

    /**
     * Removes multiple entities from a collection if they are present.
     *
     * @param  Collection  $collection  The collection to remove the entities from
     * @param  array       $entities    The entities to remove
     *
     * @return static
     */
    public function removeManyToCollection(Collection $collection, array $entities): static
    {
        foreach ($entities as $entity) {
            if ($collection->contains($entity)) {
                $collection->removeElement($entity);
            }
        }

        return $this;
    }

}
