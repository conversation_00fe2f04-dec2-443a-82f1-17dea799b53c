<?php

declare(strict_types=1);

namespace App\Entity;

use App\Base\BaseEntity;
use App\Entity\Embeddable\CreditPackage\CreditPackageMetaData;
use App\Entity\Trait\HasCollectionTrait;
use App\Repository\CreditPackageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class CreditPackage.
 *
 * Represents a credit-package in the system. Maps to the `credit_packages` table in the database.
 */
#[ORM\Entity(repositoryClass: CreditPackageRepository::class)]
#[ORM\Table(name: 'credit_packages')]
class CreditPackage extends BaseEntity
{

    use HasCollectionTrait;

    #[ORM\OneToMany(targetEntity: Client::class, mappedBy: 'creditPackage', cascade: ['persist'])]
    private Collection $clients;

    #[ORM\Embedded(class: CreditPackageMetaData::class, columnPrefix: false)]
    private CreditPackageMetaData $metaData;

    public function __construct(string $shortcut)
    {
        $this->metaData = new CreditPackageMetaData($shortcut);

        $this->clients = new ArrayCollection();
    }

    public function getClients(): Collection
    {
        return $this->clients;
    }

    public function loadMetaData(): CreditPackageMetaData
    {
        return $this->metaData;
    }

}
