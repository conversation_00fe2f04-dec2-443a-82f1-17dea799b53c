<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    05/06/2025
 * @time    15:36
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO;

/**
 * Data Transfer Object (DTO) for comprehensive account information.
 *
 * This DTO aggregates various details related to a user account, intended for
 * displaying a full profile or managing account settings within an administrative
 * interface. It includes core identification, status, and timestamp data.
 */
final class AccountInformationDTO
{
    public ?string $parent;

    public ?int $parentId = null;
    public ?string $username = null;
    public ?string $email = null;
    public ?array $roles = null;
    public ?string $registrationIp = null;
    public ?int $status = null;
    public ?string $lastLoginAt = null;
    public ?string $blockedAt = null;
    public ?string $deletedAt = null;
    public ?string $createdAt = null;
    public ?string $updatedAt = null;
}
