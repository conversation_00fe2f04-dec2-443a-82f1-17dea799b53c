<?php
/**
 * @project zenshop
 * <AUTHOR>
 * @email <EMAIL>
 * @date    06/06/2025
 * @time    15:59
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\AccountManagement\DTO\UserTypeInfo;

/**
 * Data Transfer Object (DTO) for client-specific user type information.
 *
 * This DTO encapsulates data relevant to a client user type, specifically
 * concerning subscription or service renewal status. It's designed to be
 * used where detailed client renewal information is needed, such as in
 * user profile management or billing systems.
 */
final class ClientDataDTO
{
    public ?int $isAutoRenew;
    public ?string $lastRenewAt;
}
