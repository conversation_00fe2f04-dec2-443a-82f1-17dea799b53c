server {
    listen 80;
    server_name ${DOCKER_WEB_HOST};

    root /var/www/zenshop/public;

    location / {
        # try to serve file directly, fallback to index.php
        try_files $uri /index.php$is_args$args;
    }

    # Equivalent to the Authorization header handling in .htaccess
    location ~ ^/index\.php(/|$) {
        fastcgi_pass app:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;

        # Set Authorization header from HTTP_AUTHORIZATION if present
        fastcgi_param HTTP_AUTHORIZATION $http_authorization if_not_empty;

        # Tăng timeout cho các yêu cầu lâu hơn
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;

        internal;
    }

    # Return 404 for all other php files not matching the front controller
    location ~ \.php$ {
        return 404;
    }

    # Deny access to .htaccess files
    location ~ /\.ht {
        deny all;
    }

    # Enable browser caching for static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
    }

    error_log /var/log/nginx/zenshop_error.log;
    access_log /var/log/nginx/zenshop_access.log;

    include mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    server_tokens off;
    client_max_body_size 2M;
    client_body_buffer_size 2M;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
}
