<?php
/**
 * @project zenshop-app
 * <AUTHOR>
 * @email <EMAIL>
 * @date    18/06/2025
 * @time    00:06
 */

declare(strict_types=1);

namespace App\Domain\Admin\Module\Auth\Security\Checker;

use App\Entity\Constant\UserConstant;
use App\Entity\User;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAccountStatusException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class AdminUserChecker.
 *
 * This class checks the user status and rate limits for admin users during authentication.
 * It ensures that only users with the admin role can log in and applies rate limiting to prevent brute force attacks.
 */
class AdminUserChecker implements UserCheckerInterface
{
    private RateLimiterFactory $adminLimiter;
    private RequestStack $requestStack;

    public function __construct(
        RateLimiterFactory $adminLimiter,
        RequestStack $requestStack,
    ) {
        $this->adminLimiter = $adminLimiter;
        $this->requestStack = $requestStack;
    }

    /**
     * @inheritDoc
     */
    public function checkPreAuth(UserInterface $user): void
    {
        if (!$user instanceof User) {
            return;
        }

        $request = $this->requestStack->getCurrentRequest();
        $clientIp = $request?->getClientIp();
        $limiter = $this->adminLimiter->create($clientIp);

        if (!$limiter->consume(1)->isAccepted()) {
            throw new CustomUserMessageAccountStatusException(
                'Your account has been temporarily locked due to too many failed login attempts. Please try again after 15 minutes.'
            );
        }

        if (!in_array(UserConstant::ROLE_ADMIN, $user->getRoles(), true)) {
            throw new CustomUserMessageAccountStatusException("Incorrect email or password. Please try again.");
        }

        if ($user->loadMetaData()->getStatus() !== UserConstant::STATUS_ACTIVE) {
            throw new CustomUserMessageAccountStatusException('Your account is not activated or has been locked.');
        }
    }

    /**
     * @inheritDoc
     */
    public function checkPostAuth(UserInterface $user): void
    {
    }

}
