<?php

declare(strict_types=1);

namespace App\Entity\Trait;

use Doctrine\ORM\Mapping as ORM;

/**
 * Trait HasStatusTrait.
 *
 * This trait provides any common functionality related to the status of the entity.
 */
trait HasStatusTrait
{

    #[ORM\Column(name: 'status', type: 'smallint')]
    protected int $status;

    /**
     * Check if the entity has a specific status.
     *
     * @param  int  $status  The status to check against
     *
     * @return bool True if the entity has the specified status, false otherwise
     */
    public function hasStatus(int $status): bool
    {
        return $this->getStatus() === $status;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): static
    {
        $this->status = $status;

        return $this;
    }

}
