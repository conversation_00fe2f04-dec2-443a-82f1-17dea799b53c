[mysqld]
bind-address = 0.0.0.0
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default_authentication_plugin = mysql_native_password

skip-name-resolve
max_connections = 1000
connect_timeout = 5
wait_timeout = 600
max_allowed_packet = 64M

innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
